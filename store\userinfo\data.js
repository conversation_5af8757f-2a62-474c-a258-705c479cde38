﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'dianping://api/admin/userinfo/list.htm',
            type: 'get'
        }
    }
    

    var del = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/userinfo/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'dianping://api/admin/userinfo/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/userinfo/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'dianping://api/admin/userinfo/view.htm',
            // ?mucangId=***
            type: 'get'
        }
    }
    
    var comment = {
        load: {
            url: 'dianping://api/admin/dianping/list-from-db.htm',
            type: 'get'
        }
    }
    

    var forbidden = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/userinfo/forbidden.htm',
            //?mucangId=**&forever=true/false&forbiddenDay=1 forever：是否永久禁言
            //forbiddenDay：禁言天数
            type: 'post'
        }
    }
    
    var removeBlacklist = {
        save: {
            url: 'dianping://api/admin/userinfo/remove-blacklist.htm', //?mucangId=***
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        comment: comment,
        forbidden: forbidden,
        removeBlacklist: removeBlacklist
    }

});