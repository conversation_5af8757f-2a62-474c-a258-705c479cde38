/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'dianping!dianping-image/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
             {
                 header: '点评id：',
                 dataIndex: 'dianpingId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '点评id'
             },
             {
                 header: '图片URL：',
                 dataIndex: 'url',
                 xtype: 'textarea',
                 maxlength: 1024,
                 check: 'required',
                 placeholder: '图片URL'
             },
             {
                 header: 'width：',
                 dataIndex: 'width',
                 xtype: 'text',
                 check: 'required',
                 placeholder: 'width'
             },
             {
                 header: '高：',
                 dataIndex: 'height',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '高'
             },
             {
                 header: '图片大小：',
                 dataIndex: 'contentLength',
                 xtype: 'text',
                 placeholder: '图片大小'
             }

            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '图片数据列表',
            title: '图片数据列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'dianping!dianping-image/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '点评id：',
                         dataIndex: 'dianpingId'
                     },
                     {
                         header: '图片URL：',
                         dataIndex: 'url'
                     },
                     {
                         header: 'width：',
                         dataIndex: 'width'
                     },
                     {
                         header: '高：',
                         dataIndex: 'height'
                     },
                     {
                         header: '图片大小：',
                         dataIndex: 'contentLength'
                     },
                     {
                         header: '创建时间：',
                         dataIndex: 'createTime'
                     }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'dianping!dianping-image/data/view',
                        save: 'dianping!dianping-image/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '点评id：',
                 dataIndex: 'dianpingId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '点评id'
             },
             {
                 header: '图片URL：',
                 dataIndex: 'url',
                 xtype: 'textarea',
                 maxlength: 1024,
                 check: 'required',
                 placeholder: '图片URL'
             },
             {
                 header: 'width：',
                 dataIndex: 'width',
                 xtype: 'text',
                 check: 'required',
                 placeholder: 'width'
             },
             {
                 header: '高：',
                 dataIndex: 'height',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '高'
             },
             {
                 header: '图片大小：',
                 dataIndex: 'contentLength',
                 xtype: 'text',
                 placeholder: '图片大小'
             }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'dianping!dianping-image/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '点评id',
                         dataIndex: 'dianpingId'
                     },
                     {
                         header: '图片URL',
                         dataIndex: 'url'
                     },
                     {
                         header: 'width',
                         dataIndex: 'width'
                     },
                     {
                         header: '高',
                         dataIndex: 'height'
                     },
                     {
                         header: '图片大小',
                         dataIndex: 'contentLength'
                     },
                     {
                         header: '创建时间',
                         dataIndex: 'createTime'
                     }

            ]
        }, ['dianping!dianping-image/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});