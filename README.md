## 项目的背景

驾校点评数据综合管理后台

## 项目的术语定义

无

## 项目的部署

服务器配置管理后台 - 后台发布列表 - 点评-驾校

## 项目的运行环境

1. node >= 14
2. 运行test.html
3. chrome 浏览器

## 项目的编码规范

1. 文件夹名字统一用小写字母;
2. 文件夹多个单词之间用"-"来连接，如：custom-navigation;
3. 多个单词使用驼峰命名，如：orderDetail;
4. css布局统一使用"-"来连接。如： header-wraper;
5. js 的命名采用驼峰命名法;

## 项目的技术选型
1. Simple
2. JQuery

原始项目使用JQuery，后续开发如需增加新页面，简单列表页使用JQuery，否则使用Vue3。

## 项目的逻辑视图

![点评流程图](./public-readme/dianping.png)

## 项目的注意事项

[通用注意事项](https://alidocs.dingtalk.com/i/nodes/XPwkYGxZV3zkv6mGfAZ01mXaWAgozOKL?utm_scene=person_space)

## 项目的参考资料

[Simple文档](https://note.youdao.com/ynoteshare/index.html?id=84320485be96e76a82ba94cefe47a159&type=note&_time=1703240180018)

[驾校点评管理后台](https://alidocs.dingtalk.com/i/nodes/KGZLxjv9VG67YpzLi5do4jqZW6EDybno)
