/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!app/layout/main','dianping!app/push-config/index'], function (Template, Table, Utils, Widgets, Store, Form,Layout,PushList) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'dianping!dianping-place/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '名称'
                },
                {
                    header: '描述：',
                    dataIndex: 'description',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '描述'
                },
                {
                    header: '是否需要审核：',
                    dataIndex: 'needAudit',
                    xtype: 'radio',
                    store:[
                        {key : true, value : '是'},
                        {key : false, value : '否'}
                    ]
                },
                {
                    header: '允许匿名',
                    dataIndex: 'allowAnonymous',
                    xtype: 'radio',
                    store:[
                        {key : true, value : '是'},
                        {key : false, value : '否'}
                    ]
                },

            ]
        }).add();
    };

    var list = function (panel) {
        Table({
            description: '点评位置列表',
            title: '点评位置列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'dianping!dianping-place/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '标识：',
                            dataIndex: 'token'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人名称：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人名称：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data,'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'dianping!dianping-place/data/view',
                        save: 'dianping!dianping-place/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '名称：',
                            // disabled:'disabled',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '名称'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '描述'
                        },
                        {
                            header: '是否需要审核：',
                            dataIndex: 'needAudit',
                            xtype: 'radio',
                            store:[
                                {key : true, value : '是'},
                                {key : false, value : '否'}
                            ]
                        },
                        {
                            header: '允许匿名',
                            dataIndex: 'allowAnonymous',
                            xtype: 'radio',
                            store:[
                                {key : true, value : '是'},
                                {key : false, value : '否'}
                            ]
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'dianping!dianping-place/data/delete'
                },
                {
                    name: '推送详情',
                    class: 'info',
                    click: function (table,lineDom,lineData,dom,data,index){
                        var panel = Layout.panel({
                            id: 'push-config-view'+ lineData.id,
                            name: '推送列表'+ lineData.id
                        });
                        PushList.list(panel,lineData.id);
                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '标识',
                    dataIndex: 'token'
                },

                {
                    header: '描述',
                    dataIndex: 'description'
                },
                
                {
                    header: '创建人名称',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
              
                {
                    header: '修改人名称',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['dianping!dianping-place/data/list'], panel, null).render();
    };

    return {
        list: list
    }

});