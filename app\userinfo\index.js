/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {
    var list = function (panel) {
        Table({
            description: '用户数据列表',
            title: '用户数据列表',
            search:[
                {
                    xtype:'text',
                    dataIndex: 'mucangId',
                    placeholder: '请输入mucangId'
                }
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'dianping!userinfo/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户编号：',
                            dataIndex: 'mucangId'
                        },
                        {
                            header: '昵称：',
                            dataIndex: 'nickname'
                        },
                        //Caraxu
                        {
                            header: '性别：',
                            dataIndex: 'gender'
                        },
                        {
                            header: '头像：',
                            dataIndex: 'avatar'
                        },
                        {
                            header: '最近经度：',
                            dataIndex: 'lastLongitude'
                        },
                        {
                            header: '最近纬度：',
                            dataIndex: 'lastLatitude'
                        },
                        {
                            header: 'GPS更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'gpsUpdateTime'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '最后登录时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'lastLoginTime'
                        }

                    ]
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '昵称',
                    dataIndex: 'nickname'
                },
                {
                    header: '头像',
                    dataIndex: 'avatar',
                    render: function(data){
                        return '<img src="' + data + '" style="max-width:50px; max-height:50px;">'
                    }
                },
                {
                    header: '性别',
                    dataIndex: 'gender'
                },
                {
                    header: '用户编号',
                    dataIndex: 'mucangId'
                },
                {
                    header: '最近经度',
                    dataIndex: 'lastLongitude'
                },
                {
                    header: '最近纬度',
                    dataIndex: 'lastLatitude'
                },
                {
                    header: 'GPS更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'gpsUpdateTime'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '最后登录时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'lastLoginTime'
                }

            ]
        }, ['dianping!userinfo/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});