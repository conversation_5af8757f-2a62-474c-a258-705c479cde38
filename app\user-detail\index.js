/**
 * Created by srq on 2016/3/1.
 */
"use strict";

define([
  "simple!core/template",
  "simple!core/table",
  "simple!core/utils",
  "simple!core/widgets",
  "simple!core/store",
  "simple!core/form",
  "simple!core/ajax",
  "simple!core/plugin",
  "simple!app/layout/main",
], function (
  Template,
  Table,
  Utils,
  Widgets,
  Store,
  Form,
  Ajax,
  Plugin,
  Layout
) {
  var deleteAll = function (Table) {
    //检测哪些checkbox被打勾了 记录ids
    var idsArr = [];
    var myIds = "";
    var paramStr = "";
    $("input[class='oCheck']:checkbox").each(function () {
      if ($(this)[0].checked == true) {
        myIds = $.trim($(this).parent().parent().find("td[title='#']").text()); //string   本行的#(id)
        idsArr.push(parseInt(myIds));
      }
    });

    paramStr = idsArr.join(",");
    Store(["dianping!dianping/data/batchDelete?ids=" + paramStr])
      .save()
      .done(function () {
        Table.render();
      })
      .fail(function (data) {
        Widgets.dialog.alert(data.message);
      });
  };

  var letFail = function (Table) {
    //检测哪些checkbox被打勾了 记录ids
    var idsArr = [];
    var myIds = "";
    var paramStr = "";
    $(".commentTable")
      .find("input[class='oCheck']:checkbox")
      .each(function () {
        if ($(this)[0].checked == true) {
          myIds = $.trim(
            $(this).parent().parent().find("td[title='#']").text()
          ); //string   本行的#(id)
          idsArr.push(parseInt(myIds));
        }
      });
    paramStr = idsArr.join(",");
    Store(["dianping!dianping/data/audit?ids=" + paramStr + "&status=2"])
      .save()
      .done(function () {
        Table.render();
      })
      .fail(function (data) {
        Widgets.dialog.alert(data.message);
      });
  };

  var letPass = function (Table) {
    //检测哪些checkbox被打勾了 记录ids
    var idsArr = [];
    var myIds = "";
    var paramStr = "";
    $(".commentTable")
      .find("input[class='oCheck']:checkbox")
      .each(function () {
        if ($(this)[0].checked == true) {
          myIds = $.trim(
            $(this).parent().parent().find("td[title='#']").text()
          ); //string   本行的#(id)
          idsArr.push(parseInt(myIds));
        }
      });
    paramStr = idsArr.join(",");
    Store(["dianping!dianping/data/audit?ids=" + paramStr + "&status=0"])
      .save()
      .done(function () {
        Table.render();
      })
      .fail(function (data) {
        Widgets.dialog.alert(data.message);
      });
  };

  var edit = function (panel, obj) {
    Template(
      "dianping!user/main",
      panel,
      function (dom, data, item, mainObj) {
        var oData = data.userData.data;
        console.log(oData);

        item("forbidBtn").on("click", function () {
          Table({
            title: "设置禁言时长",
            width: 500,
            store: "dianping!userinfo/data/forbidden",
            success: function (obj, dialog) {
              Widgets.dialog.alert("禁言成功！");
              dialog.close();
              mainObj.render();
            },
            columns: [
              {
                header: "木仓Id",
                dataIndex: "mucangId",
                xtype: "hidden",
                value: oData.mucangId,
              },

              {
                header: "是否永久",
                dataIndex: "forever",
                xtype: "select",
                store: [
                  {
                    key: false,
                    value: "暂时禁言",
                  },
                  {
                    key: true,
                    value: "永久禁言",
                  },
                ],
              },
              {
                header: "禁言天数",
                dataIndex: "forbiddenDay",
                xtype: "select",
                value: "",
                store: [
                  { key: 1, value: "1天" },
                  { key: 3, value: "3天" },
                  { key: 7, value: "7天" },
                ],
              },
            ],
            form: {
              submitHandler: function (form) {
                var flag = true;
                if (
                  $(form).find("#forever").val() == true &&
                  $(form).find("#forbiddenDay").val()
                ) {
                  flag = false;
                  Widgets.dialog.alert("永久禁言时不能填写禁言天数!");
                }
                if (
                  $(form).find("#forever").val() == false &&
                  !$(form).find("#forbiddenDay").val()
                ) {
                  flag = false;
                  Widgets.dialog.alert("暂时禁言时必须填写禁言天数!");
                }
                return flag;
              },
            },
          }).add();
        });
        item("releaseBtn").on("click", function () {
          Widgets.dialog.confirm("确定对Ta解禁吗？", function (e, status) {
            if (status) {
              Store([
                "dianping!userinfo/data/removeBlacklist?mucangId=" +
                  obj.authorId,
              ])
                .save()
                .done(function () {
                  Widgets.dialog.alert("成功解禁！");
                  mainObj.render();
                });
            }
          });
        });
        item("concatUser").on("click", function () {
          Table({
            title: "联系用户",
            width: 500,
            success: function (obj, dialog) {
              Widgets.dialog.alert("禁言成功！");
              dialog.close();
              mainObj.render();
            },
            renderAfter: function (table, dom) {
              var $phone = dom.find("#phone");
              $phone.parent().css("display", "flex");
              $phone.after(
                '<a class=" btn btn-info" id="phoneConfirm" style="margin-left:10px;">确认</a>'
              );

              dom.find("#phoneConfirm").on("click", function () {
                Store(["dianping!dianping/data/virtualNumber"])
                  .load([
                    {
                      aliases: "list",
                      params: {
                        mucangId: oData.mucangId,
                        kefuPhone: $phone.val(),
                      },
                    },
                  ])
                  .done(function (store, data) {
                    var vp = data.list.data.phoneX;
                    dom.find("#virtualPhone").val(vp);
                  })
                  .fail(function (data) {
                    Widgets.dialog.alert(data.message);
                  });
              });
              $(dom).parents(".modal-content").find(".modal-footer").empty();
              dom.append(
                ' <p style="color:red;text-align:center;">效期时间：五分钟</p>'
              );
            },
            columns: [
              {
                header: "主呼叫号码",
                dataIndex: "phone",
                xtype: "text",
              },
              {
                header: "虚拟号",
                xtype: "text",
                dataIndex: "virtualPhone",
                disabled: true,
              },
            ],
            form: {
              submitHandler: function (form) {},
            },
          }).add();
        });
        //黑名单过期时间格式化
        var blacklistExpiredTime = $("div")
          .find("[name=blacklistExpiredTime]")
          .val();
        blacklistExpiredTime = Utils.format.date(
          parseInt(blacklistExpiredTime),
          "yyyy-MM-dd HH:mm:ss"
        );
        $("div").find("[name=blacklistExpiredTime]").val(blacklistExpiredTime);

        var $container = $(".commentTable");

        Table(
          {
            search: [
              {
                xtype: "text",
                dataIndex: "id",
                placeholder: "请输入id",
              },
              {
                xtype: "select",
                dataIndex: "placeToken",
                index: {
                  key: "token",
                  value: "name",
                },
                store: "dianping!dianping-place/data/listAll",
                insert: [
                  {
                    name: "请选择点评位",
                    token: "",
                  },
                ],
                autoSubmit: true,
              },
              {
                xtype: "select",
                dataIndex: "status",
                store: [
                  { key: 0, value: "正常" },
                  { key: 1, value: "待审核" },
                  { key: 2, value: "已删除" },
                ],
                autoSubmit: true,
              },
              {
                xtype: "number",
                dataIndex: "topic",
                placeholder: "请输入topic",
              },
              {
                xtype: "text",
                dataIndex: "content",
                placeholder: "请输入content",
              },
            ],
            buttons: {
              top: [
                {
                  name: "刷新",
                  class: "info",
                  click: function (obj) {
                    obj.render();
                  },
                },
                {
                  name: "审核通过",
                  class: "primary",
                  click: letPass,
                },
                {
                  name: "审核失败",
                  class: "warning",
                  click: letFail,
                },
              ],
              bottom: [
                {
                  name: "刷新",
                  class: "info",
                  click: function (obj) {
                    obj.render();
                  },
                },
              ],
            },
            operations: [
              {
                name: "查看",
                xtype: "view",
                width: 400,
                class: "success",
                title: "查看",
                store: "dianping!dianping/data/view",
                columns: [
                  {
                    header: "#",
                    dataIndex: "id",
                  },
                  {
                    header: "点评位：",
                    dataIndex: "placeToken",
                  },
                  {
                    header: "topic：",
                    dataIndex: "topic",
                  },
                  {
                    header: "作者编号：",
                    dataIndex: "authorId",
                  },
                  {
                    header: "可信度",
                    dataIndex: "credibility",
                    render: (data, arr, lineData) =>
                      data === 0
                        ? (lineData.registerSameDay ? `未知：${lineData.registerSameDay}` : '未知')
                        : data === 1
                        ? "低"
                        : data === 2
                        ? "高"
                        : "数据暂未同步",
                  },
                  {
                    header: "楼层：",
                    dataIndex: "floor",
                  },
                  {
                    header: "内容：",
                    dataIndex: "content",
                  },
                  {
                    header: "图片数量：",
                    dataIndex: "imageCount",
                  },
                  {
                    header: "地点：",
                    dataIndex: "location",
                  },
                  {
                    header: "地址：",
                    dataIndex: "address",
                  },
                  {
                    header: "引用回复：",
                    dataIndex: "replyId",
                  },
                  {
                    header: "是否删除：",
                    render: function (data) {
                      if (data) {
                        return "是";
                      } else {
                        return "否";
                      }
                    },
                    dataIndex: "deleted",
                  },
                  {
                    header: "jinghua：",
                    render: function (data) {
                      if (data) {
                        return "是";
                      } else {
                        return "否";
                      }
                    },
                    dataIndex: "jinghua",
                  },
                  {
                    header: "点赞数量：",
                    dataIndex: "zanCount",
                  },
                  {
                    header: "创建时间：",
                    dataIndex: "createTime",
                  },
                  {
                    header: "appuser：",
                    dataIndex: "appuser",
                  },
                  {
                    header: "imei：",
                    dataIndex: "imei",
                  },
                  {
                    header: "appName：",
                    dataIndex: "appName",
                  },
                  {
                    header: "product：",
                    dataIndex: "product",
                  },
                  {
                    header: "version：",
                    dataIndex: "version",
                  },
                  {
                    header: "platform：",
                    dataIndex: "platform",
                  },
                  {
                    header: "system：",
                    dataIndex: "system",
                  },
                  {
                    header: "pkgName：",
                    dataIndex: "pkgName",
                  },
                  {
                    header: "device：",
                    dataIndex: "device",
                  },
                  {
                    header: "ip：",
                    dataIndex: "ip",
                  },
                  {
                    header: "longitude：",
                    dataIndex: "longitude",
                  },
                  {
                    header: "latitude：",
                    dataIndex: "latitude",
                  },
                ],
              },
              /*status==0 正常  ，，status==1，待审核，，status==2 已删除*/
              {
                name: "审核",
                class: "info",
                render: function (name, lineList, index) {
                  var status = lineList[index].status;
                  if (status == 1) {
                    return "审核通过";
                  } else {
                    return ""; //status只有在1才行，不然就不显示关于审核的按钮
                  }
                },
                click: function (table, row, lineData) {
                  var pass = true;
                  Store([
                    "dianping!dianping/data/audit?ids=" +
                      lineData.id +
                      "&status=0",
                  ])
                    .save()
                    .done(function (store, data) {
                      table.render();
                    })
                    .fail(function (data) {
                      Widgets.dialog.alert(data.message);
                    });
                },
              },
              {
                name: "审核失败",
                class: "info",
                render: function (name, lineList, index) {
                  var status = lineList[index].status;
                  if (status == 1) {
                    return "审核失败";
                  } else {
                    return ""; //status只有在1才行，不然就不显示关于审核的按钮
                  }
                },
                click: function (table, row, lineData) {
                  var pass = false;
                  Store([
                    "dianping!dianping/data/audit?ids=" +
                      lineData.id +
                      "&status=2",
                  ])
                    .save()
                    .done(function (store, data) {
                      table.render();
                    })
                    .fail(function (data) {
                      Widgets.dialog.alert(data.message);
                    });
                },
              },
              {
                name: "删除",
                class: "danger",
                render: function (name, objArr, index) {
                  var val = "";
                  if (objArr[index].status !== 2) {
                    val = "删除";
                  }
                  return val;
                },
                //xtype: 'delete',
                //store: 'dianping!dianping/data/delete',
                click: function (table, row, lineData) {
                  Widgets.dialog.confirm(
                    "确定删除 #" + lineData.id + " 这条点评吗？",
                    function (e, status) {
                      if (status) {
                        Store([
                          "dianping!dianping/data/delete?id=" + lineData.id,
                        ])
                          .save()
                          .done(function () {
                            console.log("xxxx");

                            //panel.trigger('close');
                            table.render();
                          })
                          .fail(function (data) {
                            Widgets.dialog.alert(data.message);
                          });
                      }
                    }
                  );
                },
              },
            ],
            columns: [
              {
                header: function () {
                  return '<input class="allCheck" type="checkbox">';
                },
                render: function () {
                  return '<input class="oCheck" type="checkbox">';
                },
              },
              {
                header: "用户头像",
                dataIndex: "author",
                render: function (data) {
                  return (
                    '<img src="' +
                    data.avatar +
                    '" style="max-width:50px; max-height:50px;">'
                  );
                },
              },
              {
                header: "昵称",
                dataIndex: "author",
                render: function (data, table, row) {
                  return (
                    '<span style=color:' + (row.dianpingRed ? 'red' : '#33333')+ '>' + data.nickname + '</span>'
                  );
                },
              },
              {
                header: "可信度",
                dataIndex: "credibility",
                render: (data, arr, lineData) =>
                    data === 0
                      ? (lineData.registerSameDay ? `未知：${lineData.registerSameDay}` : '未知')
                      : data === 1
                      ? "低"
                      : data === 2
                      ? "高"
                      : "数据暂未同步",
                },
              {
                header: "#",
                dataIndex: "id",
                width: 20,
              },
              {
                header: "点评位",
                dataIndex: "placeName",
              },
              {
                header: "点评主题",
                dataIndex: "topic",
              },
              {
                header: "楼层",
                dataIndex: "floor",
              },
              {
                header: "内容",
                dataIndex: "content",
              },
              {
                header: "图片数量",
                dataIndex: "imageCount",
              },
              {
                header: "地点",
                dataIndex: "location",
              },
              {
                header: "地址",
                dataIndex: "address",
              },
              {
                header: "引用回复",
                dataIndex: "replyId",
              },
              /*{
                     header: '是否删除',
                     render: function (data) {
                     if (data) {
                     return '是';
                     } else {
                     return '否';
                     }
                     },
                     dataIndex: 'deleted'
                     },*/
              {
                header: "状态",
                dataIndex: "status",
                render: function (data) {
                  var map = [
                    { key: 0, value: "正常" },
                    { key: 1, value: "待审核" },
                    { key: 2, value: "已删除" },
                  ];
                  var val = "";
                  var styleStr = "";
                  $.each(map, function (i, obj) {
                    if (data == obj.key) {
                      val = obj.value;
                    }
                  });
                  if (val == "已删除") {
                    styleStr = ' style="color: orangered;"';
                  }

                  //return '<img src="' + data.avatar + '" style="max-width:50px; max-height:50px;">'
                  return "<span" + styleStr + " >" + val + "</span>";
                },
              },
              {
                header: "是否精华",
                render: function (data) {
                  if (data) {
                    return "是";
                  } else {
                    return "否";
                  }
                },
                dataIndex: "jinghua",
              },
              {
                header: "点赞数量",
                dataIndex: "zanCount",
              },
              {
                header: "创建时间",
                dataIndex: "createTime",
                render: function (data) {
                  return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                },
              },
            ],
          },
          ["dianping!userinfo/data/comment?status=0&authorId=" + obj.authorId],
          $container,
          function () {
            $(".allCheck").on("click", function () {
              $(".oCheck").click();
            });
          }
        ).render();
      },
      [
        "dianping!userinfo/data/view?mucangId=" + obj.authorId,
        "dianping!userinfo/data/comment?authorId=" + obj.authorId,
      ]
    ).render([{ aliases: "userData" }, { aliases: "commentData" }]);
  };

  return {
    edit: edit,
  };
});
