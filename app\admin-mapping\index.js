/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'dianping!admin-mapping/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [

                {
                    header: '手机号码：',
                    dataIndex: 'phoneNumber',
                    xtype: 'text',
                    maxlength: 64,
                    check: 'required',
                    placeholder: '手机号码'
                },
                {
                    header: 'SSO编号：',
                    dataIndex: 'ssoId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: 'SSO编号'
                }


            ]
        }).add();
    };

    var list = function (panel) {
        Table({
            description: '管理员映射列表',
            title: '管理员映射列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'dianping!admin-mapping/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户名：',
                            dataIndex: 'username'
                        },
                        {
                            header: '木仓编号：',
                            dataIndex: 'mucangId'
                        },
                        {
                            header: 'SSO编号：',
                            dataIndex: 'ssoId'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人名称：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            dataIndex: 'createTime'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人名称：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '修改时间：',
                            dataIndex: 'updateTime'
                        }

                    ]
                },
               /* {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'dianping!admin-mapping/data/view',
                        save: 'dianping!admin-mapping/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '用户名：',
                            dataIndex: 'username',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '用户名'
                        },
                        {
                            header: '木仓编号：',
                            dataIndex: 'mucangId',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '木仓编号'
                        },
                        {
                            header: 'SSO编号：',
                            dataIndex: 'ssoId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: 'SSO编号'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '描述'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '创建人'
                        },
                        {
                            header: '创建人名称：',
                            dataIndex: 'createUserName',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '创建人名称'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '修改人'
                        },
                        {
                            header: '修改人名称：',
                            dataIndex: 'updateUserName',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '修改人名称'
                        },
                        {
                            header: '修改时间：',
                            dataIndex: 'updateTime',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '修改时间'
                        }

                    ]
                },*/
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'dianping!admin-mapping/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户名',
                    dataIndex: 'username'
                },
                {
                    header: '木仓编号',
                    dataIndex: 'mucangId'
                },
                {
                    header: 'SSO编号',
                    dataIndex: 'ssoId'
                },
                {
                    header: '描述',
                    dataIndex: 'description'
                },
                {
                    header: '修改人名称',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    dataIndex: 'updateTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                }
            ]
        }, ['dianping!admin-mapping/data/list'], panel, null).render();
    };

    return {
        list: list
    }

});