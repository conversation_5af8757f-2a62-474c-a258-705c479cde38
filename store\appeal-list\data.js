﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'dianping://api/admin/dianping-complain/list-complain.htm',
            type: 'get'
        }
    }

    var update = {
        primary: 'ids',
        save: {
            url: 'dianping://api/admin/dianping-complain/complain-audit.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'dianping://api/admin/dianping-complain/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        update: update,
        view: view
    }

});