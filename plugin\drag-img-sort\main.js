﻿/*
 * drag-sort v0.0.1
 *
 * name: GG
 * date: 2014/02/28
 */

"use strict";

define(['simple!core/template', 'simple!core/widgets', 'simple!core/store'], function (Template, Widgets, Store) {

    var Sort = function (plugin, success) {
        this.plugin = plugin;
        this.success = success;
        this.config = plugin.config;
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    Sort.prototype = {

        render: function () {
            var me = this;
            Widgets.dialog.html('调整排序', {
                width: 500,
                buttons: [
                    {
                        name: '保存',
                        xtype: 'success',
                        click: function () {
                            var dialog = this;
                            var ids = [];
                            dialog.body.find('li').each(function () {
                                ids.push($(this).attr('data-id'));
                            });
                            Store([me.config.save]).save([
                                {params: {ids: ids.join()}}
                            ]).done(function () {
                                dialog.close();
                                me.success();
                            });
                        }
                    },
                    {
                        name: '取消',
                        xtype: 'danger',
                        click: function () {
                            this.close();
                        }
                    }
                ]
            }).done(function (dialog) {
                Template(me.plugin.path + '/template/main/list', dialog.body, function (dom, data, item, obj) {
                    dom.find('ul').sortable().disableSelection();

                    dom.delegate('[data-item="goto-btn"]', 'click', function () {
                        $(this).addClass('hidden').next().removeClass('hidden').next().removeClass('hidden');
                    });

                    dom.delegate('[data-item="go-btn"]', 'click', function () {
                        var $me = $(this).parents('li');
                        $me.item('goto-btn').removeClass('hidden');
                        $me.find('.edit').addClass('hidden');

                        var $target = dom.find('li').eq(parseInt($me.find('input').val()) - 1);
                        if ($target.length > 0) {
                            if ($target.index() < $me.index()) {
                                $me.insertBefore($target)
                            } else {
                                $me.insertAfter($target);
                            }
                        }
                    });

                }, [me.config.load]).render([
                    {
                        aliases: 'list'
                    }
                ]);
            });
        }
    }

    Sort.prototype.constructor = Sort;

    return function (plugin, success) {
        return new Sort(plugin, success);
    }

});