/*
 * data v0.0.1
 *
 * name: GG
 * date: 2014/03/28
 */

"use strict";

define(function() {

    var tableMapping = {
	"DianpingImageEntity":{
		"columns":{
			"dianpingId":"点评id",
			"createTime":"创建时间",
			"contentLength":"图片大小",
			"url":"图片URL",
			"height":"高"
		},
		"cname":"图片数据"
	},
	"DianpingEntity":{
		"columns":{
			"imageCount":"图片数量",
			"content":"内容",
			"replyId":"引用回复",
			"floor":"楼层",
			"address":"地址",
			"authorId":"作者编号",
			"deleted":"是否删除",
			"createTime":"创建时间",
			"placeToken":"点评位",
			"location":"地点"
		},
		"cname":"点评记录"
	},
	"DianpingPlaceEntity":{
		"columns":{
			"createUserId":"创建人",
			"createTime":"创建时间",
			"updateUserId":"修改人",
			"updateUserName":"修改人名称",
			"name":"名称",
			"description":"描述",
			"createUserName":"创建人名称",
			"updateTime":"修改时间",
			"token":"标识"
		},
		"cname":"点评位置"
	},
	"ZanRecordEntity":{
		"columns":{
			"dianpingId":"点评id",
			"createTime":"创建时间",
			"mucangId":"用户id"
		},
		"cname":"点赞记录"
	},
	"UserinfoEntity":{
		"columns":{
			"lastLoginTime":"最后登录时间",
			"gender":"性别",
			"lastLongitude":"最近经度",
			"createTime":"创建时间",
			"nickname":"昵称",
			"mucangId":"用户编号",
			"gpsUpdateTime":"GPS更新时间",
			"avatar":"头像",
			"lastLatitude":"最近纬度"
		},
		"cname":"用户数据"
	}
};


    return {
        tableMapping: tableMapping
    }

});
