/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */
"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    // 图片预览
    var popViewImage = function (target, lineData, index) {

        Plugin('simple!image-processing', {
            container: document.body,
            bucket: CONSTANT.uploadBucket,
            uploadUrl: CONSTANT.uploadBase64Url
        }).render().done(function (plugin) {
            this.render();
        });
        var image = new Image();
        var imageData;
        if (lineData.imageJson) {
            imageData = JSON.parse(lineData.imageJson);
            console.log(imageData);
        }

        var newImagesData = [];
        for (var i = 0; i < imageData.length; i++) {
            newImagesData[i] = {};
            newImagesData[i].url = {};
            newImagesData[i].url.original = imageData[i] + '!max800';
        }
        var loadImage = function () {
            var count = 0;
            for (var i = 0; i < newImagesData.length; i++) {
                var image = new Image();
                image.src = newImagesData[i];
                image.onload = (function (data, image) {
                    return function () {
                        count++;
                        if (count == newImagesData.length) {
                            viewImage()
                        }
                    }
                })(newImagesData[i], image);
                image.onerror = (function (data, image) {
                    return function () {
                        count++;
                        if (count == newImagesData.length) {
                            viewImage()
                        }
                    }
                })(newImagesData[i], image)
            }
        };
        loadImage();
        var viewImage = function () {
            Plugin('jiakao!view-image', {
                data: newImagesData,
                container: target,
                selector: '[data-image]',
                trigger: true,
                index: index,
                filter: true
            }).render()
        }
    }
    var typeStore =[
        {
            "value": "请选择投诉类型",
            "key": ""
        },
        {
            "value": "违法违规",
            "key": "1"
        },
        {
            "value": "涉黄低俗",
            "key": "2"
        },
        {
            "value": "垃圾广告",
            "key": "3"
        },
        {
            "value": "脏话辱骂",
            "key": "4"
        },
        {
            "value": "凑字乱打",
            "key": "5"
        },
        {
            "value": "无关内容",
            "key": "6"
        },
        {
            "value": "涉政",
            "key": "7"
        },
        {
            "value": "其他",
            "key": "8"
        }
    ]
    const AppealType = {
        // 通过
        PASS: true,
        REJECT: false
    }
    // 投诉列表
    const complaintListColumns =  [
        {
            header: '编号',
            dataIndex: 'id',
            width: 20
        },
        {
            header: '主题信息',
            dataIndex: 'topic',
            width: 150,
            render: function (data, arrData, lineData, index) {
                return (lineData.placeName || '') + "<br/>" + (lineData.topic || '')
            },
        },
        {
            header: '点评id',
            dataIndex: 'dianpingId',
        },
        {
            header: "学员评分",
            dataIndex: "score",
            width: 90,
        },
        {
            header: '点评内容',
            dataIndex: 'originalContent',
            width: 200,
            render: function (data, arrData, lineData, index) {
                return lineData.originalContent ? lineData.originalContent : lineData.dianpngContent
            },
        },
        {
            header: '回复内容',
            dataIndex: 'replyContent',
            width: 200
        },
        {
            header: '投诉类型',
            dataIndex: 'typeName',
        },
        {
            header: '追评内容',
            dataIndex: 'appendDianping',
            width: 200
        },
        {
            header: '投诉原因',
            dataIndex: 'content'
        },
        {
            header: '投诉图片',
            dataIndex: 'imageList',
            width: 100,
            render: function (data, arrData, lineData, index) {
                var str = ''
                lineData.imageCount > 0
                ? str = "共" + lineData.imageCount + "张图片"
                : "";
                if (lineData.videoUrl) {
                str+= "<br/>1个视频"
                } else {
                str+= ""
                }
                return '<a>' + str + '</a>';
            },
            click: function (table, row, lineData) {
                var str = "";
                var count = 0;
                if(lineData.imageList && lineData.imageList.length > 0){
                for (var i = 0; i <  lineData.imageList.length; i++) {
                    var imageItem = JSON.parse(lineData.imageList[i].url)
                    count++;
                    str +=
                    '<img style="" src="' +
                    imageItem.url +
                    '!max800" width="300" height="300">' +
                    "第" +
                    count +
                    "张";
                }
                }
                if (lineData.videoUrl) {
                str = str + '<br/><video width="300" src="'+lineData.videoUrl+'" controls="controls"><source type="video/mp4" src="'+lineData.videoUrl+'" /></video>'
                }
                Widgets.dialog.html("共" + lineData.imageCount + "张图片", str, {
                width: 800,
                });
            },
        },
        {
            header: '审核原因',
            dataIndex: 'auditMessage',
        },
        {
            header: '审核时间',
            dataIndex: 'auditTime',
            render: function (data) {
                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
            }
        },
        {
            header: '提交时间',
            dataIndex: 'createTime',
            render: function (data) {
                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
            }
        }
    ]
    const listColumns = [
        {
            header: '编号',
            dataIndex: 'id',
            width: 20
        },
        {
            header: '主题信息',
            dataIndex: 'topic',
            width: 150,
            render: function (data, arrData, lineData, index) {
                return (lineData.placeName || '') + "<br/>" + (lineData.topic || '')
            },
        },
        {
            header: '点评id',
            dataIndex: 'dianpingId',
        },
        {
            header: "学员评分",
            dataIndex: "score",
            width: 90,
        },
        {
            header: '点评内容',
            dataIndex: 'originalContent',
            width: 200,
            render: function (data, arrData, lineData, index) {
                return lineData.originalContent ? lineData.originalContent : lineData.dianpngContent
            }
        },
        {
            header: '回复内容',
            dataIndex: 'replyContent',
            width: 200
        },
        {
            header: '追评内容',
            dataIndex: 'appendDianping',
            width: 200
        },
        {
            header: '投诉类型',
            dataIndex: 'typeName',
        },
        {
            header: '投诉原因',
            dataIndex: 'content'
        },
        {
            header: '投诉图片',
            dataIndex: 'imageList',
            width: 100,
            render: function (data, arrData, lineData, index) {
                var str = ''
                lineData.imageCount > 0
                ? str = "共" + lineData.imageCount + "张图片"
                : "";
                if (lineData.videoUrl) {
                str+= "<br/>1个视频"
                } else {
                str+= ""
                }
                return '<a>' + str + '</a>';
            },
            click: function (table, row, lineData) {
                var str = "";
                var count = 0;
                if(lineData.imageList && lineData.imageList.length > 0){
                    for (var i = 0; i <  lineData.imageList.length; i++) {
                        var imageItem = JSON.parse(lineData.imageList[i].url)
                        count++;
                        str +=
                        '<img style="" src="' +
                        imageItem.url +
                        '!max800" width="300" height="300">' +
                        "第" +
                        count +
                        "张";
                    }
                    }
                if (lineData.videoUrl) {
                str = str + '<br/><video width="300" src="'+lineData.videoUrl+'" controls="controls"><source type="video/mp4" src="'+lineData.videoUrl+'" /></video>'
                }
                Widgets.dialog.html("共" + lineData.imageCount + "张图片", str, {
                width: 800,
                });
            },
        },
        {
            header: '提交时间',
            dataIndex: 'createTime',
            render: function (data) {
                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
            }
        }
    ]
    // 待审核投诉-列表
    var list = function (panel) {
        Table({
        description: '待审核投诉列表',
        title: '待审核投诉列表',
        search: [
            {
                xtype: 'text',
                dataIndex: 'dianpingId',
                placeholder: '请输入id'
            },
            {
                xtype: 'text',
                dataIndex: 'topic',
                placeholder: '请输入topic'
            },
            {
                xtype: 'text',
                dataIndex: 'authorId',
                placeholder: '请输入authorId'
            },
            {
                xtype: 'select',
                dataIndex: 'type',
                placeholder: '请选择投诉类型',
                store: typeStore
            },
        ],
        buttons: {
            top: [
                {
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }
            ],
            bottom: [
                {
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }
            ]
        },
        operations: [
            {
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'dianping!dianping-pending-complaint/data/view',
                columns: listColumns
            },
            {
                name: '申诉通过',
                class: 'info',
                click: function (table, lineDom, lineData) {
                    Widgets.dialog.html('确定审核通过并删除该评价及回复吗？','<textarea id="failReason" placeholder="输入通过原因，非必填" cols="50" />', {
                    width: 400,
                    buttons: [{
                        name: '确认',
                        xtype: 'success',
                        click: function () {
                        const that = this;
                        const failReason = $('#failReason').val().trim();
                        Simple.Store2('dianping!dianping-pending-complaint/data/update', {
                            id: lineData.id,
                            pass: AppealType.PASS,
                            auditMessage: failReason
                        }, 'save').then(function () {
                            that.close();
                            table.render();
                            },function (data) {
                            Widgets.dialog.alert(data.message);
                            });
                        }
                    }, {
                        name: '关闭',
                        xtype: 'primary',
                        click: function () {
                            this.close();
                        }
                    }]
                    })
                }
            },
            {
                name: '申诉失败',
                class: 'danger',
                click: function (table, lineDom, lineData) {
                    Widgets.dialog.html('确定审核失败吗？','<textarea id="failReason" placeholder="输入失败原因，非必填" cols="50" />', {
                    width: 400,
                    buttons: [{
                        name: '确认',
                        xtype: 'success',
                        click: function () {
                            const failReason = $('#failReason').val().trim();
                            const that = this;
                            Simple.Store2('dianping!dianping-pending-complaint/data/update', {
                            id: lineData.id,
                            pass: AppealType.REJECT,
                            auditMessage: failReason
                            }, 'save').then(function () {
                                table.render();
                                that.close();
                            },function (data) {
                                Widgets.dialog.alert(data.message);
                            });
                        }
                    }, {
                        name: '关闭',
                        xtype: 'primary',
                        click: function () {
                            this.close();
                        }
                    }]
                    }).done(function (dialog) {
                    })
                }
            }
        ],
        columns: listColumns
        }, ['dianping!dianping-pending-complaint/data/list?status=1'], panel, function (dom, obj, item, table) {}).render();
    }
    // 投诉列表
    var complaintList = function (panel) {
        Table({
            description: '投诉列表',
            title: '投诉列表',
            search: [
                {
                xtype: 'text',
                dataIndex: 'dianpingId',
                placeholder: '请输入id'
                },
                {
                xtype: 'text',
                dataIndex: 'topic',
                placeholder: '请输入topic'
                },
                {
                xtype: 'text',
                dataIndex: 'authorId',
                placeholder: '请输入authorId'
                },
                {
                    xtype: 'select',
                    dataIndex: 'type',
                    placeholder: '请选择投诉类型',
                    store: typeStore
                },
            ],
            buttons: {
                top: [
                {
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                    obj.render();
                    }
                }
                ],
                bottom: [
                {
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                    obj.render();
                    }
                }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'dianping!dianping-pending-complaint/data/view',
                    columns: complaintListColumns
                }
            ],
            columns: complaintListColumns
        }, ['dianping!dianping-pending-complaint/data/list'], panel, function (dom, obj, item, table) {}).render();
    }
    return {
        list: list,
        complaintList: complaintList
    }
});