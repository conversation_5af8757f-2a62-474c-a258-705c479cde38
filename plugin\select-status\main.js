"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/store', 'simple!core/form', 'simple!core/utils'], function (Template, Table, Store, Form, Utils) {

    var search = function (plugin, success) {
        this.plugin = plugin;
        this.config = this.plugin.config;
        this.success = success;
        // 默认配置
        this.config.buttons = $.extend([
            {
                key: 'createTime',
                value: '最新发布'
            },
            {
                key: 'lastCommentTime',
                value: '最后回复'
            },
            {
                key: 'commentCount',
                value: '回复多'
            },
            {
                key: 'zanCount',
                value: '赞多'
            }
        ], plugin.config.column);
        this.config.value = this.config.value || '';
        this.render();
    }
    search.prototype = {
        render: function () {
            var me = this;
            if (me.config.value) {
                var filter = me.config.value;
            } else {
                filter = 0;
            }
            var buttons = me.plugin.config.buttons;
            for (var i = 0; i < buttons.length; i++) {
                if (buttons[i].key == filter) {
                    me.plugin.config.buttons[i].active = 'active';
                    me.plugin.config.buttons[i].class = 'info';
                } else {
                    me.plugin.config.buttons[i].active = '';
                }
            }
            Template(me.plugin.path + '/template/main/search', me.plugin.config.target, function (dom, data, item) {
                dom.find('button').on('click', function () {
                    if (filter == $(this).attr('data-key')) {
                        return;
                    }
                    filter = $(this).attr('data-key');
                    dom.find('input').val(filter).parents('form').submit();
                });
            }, {
                config: me.config
            }).render();
        }
    }

    search.prototype.constructor = search;

    return function (plugin, success) {
        return new search(plugin, success);
    }

});