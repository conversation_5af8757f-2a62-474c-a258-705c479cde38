<!--
 * @Author: pengyun <EMAIL>
 * @Date: 2022-06-29 14:12:53
 * @LastEditors: pengyun <EMAIL>
 * @LastEditTime: 2022-09-08 10:00:18
 * @FilePath: /dianping-jiaxiao/index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>加载中...</title>
</head>
<body>

<script>

    // 所有配置的路径最后都不以 "/" 结尾，程序会自动增加
    !function () {

        // 加载成功之后执行的方法
        var success = function () {

        }

        var config = {
            title: '点评系统',
            name: 'dianping',
            debug: false,
                 name: "dianping",

            host: {
//                "dianping": 'http://dianping.ttt.mucang.cn',
                "dianping": 'https://dianping-jiaxiao.kakamobi.cn',
                local: 'https://dianping-jiaxiao.kakamobi.cn',
                'firewall': 'https://forbidden-words.kakamobi.cn'
            },

            // 前端文件根目录，最后没有 "/"
            root: {
                "dianping": window.location.href.replace(/\/[^/]*$/ig, ''),
                'firewall': 'https://admin.mucang.cn/forbidden.words.kakamobi.cn',
                'systemadmin': 'https://admin.mucang.cn/simple.kakamobi.com/systemadmin',
            },

            // 框架远程文件配置
            framework: {
                root: 'https://static.kakamobi.cn/simple-framework',
                version: '1.1'
            },

            // 入口文件需要的JS和CSS
            file: {
                js: [],
                css: []
            }

        }

        var script = document.createElement('script');
        script.src = config.framework.root + '/v' + config.framework.version + '/resources/js/require.min.js';
        document.body.appendChild(script);

        script.onload = function () {
            require([config.framework.root + '/v' + config.framework.version + '/app/main.js'], function (app) {
                app.init(config, success);
            });
        }

    }();

</script>

</body>
</html>
