/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
*/

"use strict";

define(['simple!core/template', 'simple!core/widgets', 'cheyouquan!app/district/district', 'simple!core/form'], function (Template, Widgets, District, Form) {

    var district = function (plugin, success) {
        this.plugin = plugin;
        this.config = plugin.config;
        this.config.name = this.config.name || [];
        this.config.change = this.config.change || $.noop;

        if (this.config.value) {
            var province = District.getProvinceOfCity(this.config.value);
            if (province && province.code) {
                this.proCode = province.code;
                this.cityCode = this.config.value;
            } else {
                this.proCode = this.config.value;
                this.cityCode = '';
            }
        }

        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');

        this.render();

        success();

    }

    district.prototype = {

        // 渲染省份
        render: function () {

            var me = this;
            var config = me.config;
            var insert = config.insert && config.insert.province;
            var select = me.province = me.createDom(District.provinces(), 'province', insert, me.proCode).select;
            var dom = (config.render && config.render(select)) || select.wrap('<div class="col-sm-6" style="padding-left: 0; padding-right: 2.5px;"></div>').parent();

            select.on('change', function () {
                me.renderCity(this.value);
            });

            me.target.empty();
            me.target.append(dom);

            me.renderCity(select.val());

        },

        // 渲染城市
        renderCity: function (code) {

            var me = this;
            var config = me.config;
            var insert = config.insert && config.insert.city;
            var elem = me.createDom(District.getCitysOfProvince(code), 'citys', insert, me.cityCode);
            var city = me.target.item(me.id + '-citys');

            if (city.size() > 0) {
                city.html(elem.option).trigger('change');
                me.city = city;
            } else {

                var select = me.city = elem.select;
                var dom = (config.render && config.render(select)) || select.wrap('<div class="col-sm-6" style="padding-left: 1.5px; padding-right: 0;"></div>').parent();

                select.on('change', function () {
                    me.createName(this.value);
                    config.change.apply(this, arguments);
                });

                me.target.append(dom);

            }

            me.createName(me.city.val());

        },

        // 创建DOM
        createDom: function (list, type, insert, defCode) {

            list = list || [];
            insert = insert || [];

            var me = this;
            var config = me.config;
            var option = '';

            for (var i = 0; i < insert.length; i++) {
                var selected = '';
                if (defCode === insert[i].code) {
                    selected = 'selected';
                }
                option += '<option ' + selected + ' value="' + insert[i].code + '">' + insert[i].name + '</option>';
            }

            for (var i = 0; i < list.length; i++) {
                var selected = '';
                if (defCode === list[i].code) {
                    selected = 'selected';
                }
                option += '<option ' + selected + ' value="' + list[i].code + '">' + list[i].name + '</option>';
            }

            var select = $('<select data-item="' + me.id + '-' + type + '" class="form-control"></select>');
            config.style && select.css(config.style);
            select.html(option);

            return {
                option: option,
                select: select
            };

        },

        createName: function (value) {
            var me = this;
            if (value) {
                me.province.removeAttr('name');
                me.city.attr('name', me.config.name);
            } else {
                me.province.attr('name', me.config.name);
                me.city.removeAttr('name');
            }
        }

    }

    district.prototype.constructor = district;

    return function (plugin, success) {
        return new district(plugin, success);
    }

});