﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'dianping://api/admin/dianping-reply/list.htm', // 回复列表
            type: 'get',
            format: function (data) {

                data.map(function (item, index) {

                    if (item.content && (item.content.search(/<script.*>/gi) > -1)) {
                        console.log(item.content)
                        item.content = item.content.replace(/<script.*>?/gi, '');
                    }

                    if (item.content && (item.content.search(/<\/script>/gi) > -1)) {
                        item.content = item.content.replace(/<\/script>/gi, '');
                    }
                    return item;
                });
                return data;
            }
        }
    }


    var listToAudit = {
        load: {
            url: 'dianping://api/admin/dianping-reply/list-review.htm', // 待审核列表
            type: 'get'
        }
    }

    var del = {
        primary: 'idList',
        save: {
            url: 'dianping://api/admin/dianping-reply/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'dianping://api/admin/dianping-reply/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/dianping-reply/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'dianping://api/admin/dianping-reply/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        listToAudit: listToAudit,
        delete: del,
        update: update,
        insert: insert,
        view: view
    }

});