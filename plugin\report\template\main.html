﻿<!--
    * report v1.0
    *
    * name: xiaojia
    * date: 2013/10/12
-->

<!--main-->
<script id="dianping-plugin-report-template-main-index" data-template='["simple!table/search"]' type="text/html">

    <div>
        
        <?include('dianping-plugin-report-template-main-title') ?>
        
        <div class="clearfix">
            <?include('dianping-plugin-report-template-main-button') ?>
            <?include('simple-template-table-search') ?>
        </div>

        <div data-item="charts" style="margin-top: 20px;"><div class="loader"></div></div>
        <div data-item="table" style="margin-top: 20px;"></div>
    
    </div>

</script>

<!--标题-->
<script id="dianping-plugin-report-template-main-title" type="text/html">

    <div class="page-header" style="margin: 0 0 10px;">
		<h3 style="margin: 0;"><? if (config.title) { ?><?=config.title ?><? } ?> <small><? if (config.description) { ?><?=config.description ?><? } ?></small></h3>
	</div>

</script>

<!--时间搜索-->
<script id="dianping-plugin-report-template-main-period" type="text/html">

    <? if (config.selector) { ?>
        <div class="form-group">
            <? for (var i = 0; i < config.selector.length; i++) { ?>
                <button type="button" data-item="preiod-selector" class="btn btn-<?=config.selector[i].class || 'default' ?>" <? if (config.selector[i].default === true) { ?>data-default<? } ?> value="<?=config.selector[i].key ?>"><?=config.selector[i].value ?></button>
            <? } ?>
        </div>
    <? } ?>
    <div class="form-group" style="width: 200px;">
        <div class="input-group">
            <input type="datetimepicker-date" name="<?=config.dataIndex[0] ?>" data-item="from" class="form-control" placeholder="开始时间">
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
            </span>
        </div>
    </div>
    <div class="form-group" style="width: 200px;">
        <div class="input-group">
            <input type="datetimepicker-date" name="<?=config.dataIndex[1] ?>" data-item="to" class="form-control" placeholder="结束时间">
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
            </span>
        </div>
    </div>

</script>

<!--button-->
<script id="dianping-plugin-report-template-main-button" type="text/html">
    
    <? if (config.buttons && config.buttons.length > 0) { ?>
        <div class="form-inline pull-left" data-item="buttons">
            <? for (var i = 0; i < config.buttons.length; i++) { ?>  
                <button type="button" data-index="<?=i ?>" class="btn btn-<?=config.buttons[i].class || 'default' ?>"><?=config.buttons[i].name ?></button>
            <? } ?>
        </div>
    <? } ?>

</script>