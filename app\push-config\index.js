/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {
    var oId;
    var add = function (table) {
        
        Table({
            title: '添加',
            width: 500,
            store: 'dianping!push-config/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '点评位：',
                    dataIndex: 'dianpingPlaceId',
                    xtype: 'number',
                    value: oId,
                    readonly: 'readonly', //这里不可以用disabled，因为disabled的数据是不会发送出去的
                    check: 'required',
                    placeholder: '点评位'
                },
                {
                    header: '推送平台：',
                    dataIndex: 'platform',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '推送平台'
                },
                {
                    header: '推送应用：',
                    dataIndex: 'appName',
                    xtype: 'textarea',
                    maxlength: 512,
                    check: 'required',
                    placeholder: '推送应用'
                },
                {
                    header: '描述：',
                    dataIndex: 'description',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '描述'
                }

            ]
        }).add();
    };

    var list = function (panel,id) {
        oId = id;
        Table({
            description: '推送列表',
            title: '推送列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'dianping!push-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '点评位：',
                            dataIndex: 'dianpingPlaceId'
                        },
                        {
                            header: '推送平台：',
                            dataIndex: 'platform'
                        },
                        {
                            header: '推送应用：',
                            dataIndex: 'appName'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人名称：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            dataIndex: 'createTime'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人名称：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '修改时间：',
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'dianping!push-config/data/view',
                        save: 'dianping!push-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '点评位：',
                            dataIndex: 'dianpingPlaceId',
                            readonly: 'readonly',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '点评位'
                        },
                        {
                            header: '推送平台：',
                            dataIndex: 'platform',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '推送平台'
                        },
                        {
                            header: '推送应用：',
                            dataIndex: 'appName',
                            xtype: 'textarea',
                            maxlength: 512,
                            check: 'required',
                            placeholder: '推送应用'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '描述'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'dianping!push-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '点评位',
                    dataIndex: 'dianpingPlaceId'
                },
                {
                    header: '推送平台',
                    dataIndex: 'platform'
                },
                {
                    header: '推送应用',
                    dataIndex: 'appName'
                },
                {
                    header: '描述',
                    dataIndex: 'description'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人名称',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    dataIndex: 'createTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人名称',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    dataIndex: 'updateTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                }

            ]
        }, ['dianping!push-config/data/list?dianpingPlaceId='+id], panel, null).render();
    };

    return {
        list: list
    }

});