/*
 * data v0.0.1
 *
 * name: xia<PERSON>ji<PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'dianping://api/admin/dianping-appeal/list.htm',
            type: 'get'
        }
    }

    var update = {
        primary: 'ids',
        save: {
            url: 'dianping://api/admin/dianping-appeal/audit.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'dianping://api/admin/dianping-appeal/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        update: update,
        view: view
    }

});