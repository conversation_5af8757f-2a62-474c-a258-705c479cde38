﻿<!--
    * main v1.0
    *
    * name: GG
    * date: 2014/03/13
-->

<script id="cheyouquan-plugin-select-image-template-main-select" type="text/html">

    <style>

        .plugin-select-image {
            margin: 0 auto;
        }

        .plugin-select-image .img-frame {
            position: relative;
            overflow: hidden;
            border: 1px solid #DDDDDD;
            display: table-cell;
            vertical-align: middle;
        }

        .plugin-select-image .img-frame img {
            width: 100%;
        }

        .plugin-select-image .img-frame .upload-process {
            position: absolute;
            width: 100%;
            line-height: 30px;
            bottom: -30px;
            left: 0;
            text-align: center;
            color: white;
            background-color: #292929;
            opacity: 0.8;
        }

        .plugin-select-image button {
            position: relative;
            margin-top: 10px;
            width: 100%;
        }

        .plugin-select-image button input {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
        }
    </style>

    <div class="plugin-select-image" style="width: <?=config.width || 150 ?>px;">
        <div class="img-frame" style="height: <?=config.height ||150 ?>px; width: <?=config.width || 150 ?>px;">
            <img src="<?=config.value ?>" class="<?=config.value ? '' : 'hidden' ?>"/>
            <span class="upload-process"></span>
        </div>
        <p class="hidden" style="color:#ff0000"></p>
        <button type="button" class="btn btn-xs btn-info">
            <i><?=config.btnName || '选择图片' ?></i>
            <input type="file" accept="image/gif, image/jpeg, image/jpg, image/png"/>
        </button>
        <input type="hidden" data-item="plugin-image-input" name="<?=config.dataIndex || config.column.dataIndex ?>"
               value="<?=config.value?config.value.replace(/(!\d+\.\d+)$/g, ''):'' ?>" />
    </div>
</script>