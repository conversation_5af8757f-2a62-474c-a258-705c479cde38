"use strict";

define([
  "simple!core/table",
  "simple!core/utils",
  "simple!core/widgets",
  "simple!core/store",
  "simple!app/layout/main",
  "dianping!app/user-detail/index",
  "simple!core/plugin",
], function (Table, Utils, Widgets, Store, Layout, UserDetail, Plugin) {
  // 点评placeToken查询
  var searchTokenStore = [
    {
      key: "f1c24bac914c468bbdf47b7d9802f8e7",
      value: "驾校",
    },
    {
      key: "07c6d1dc5912405580584c2d02952036",
      value: "教练",
    },
  ];
  // 替换文字换行
  const regBrText = function (text) {
    if (text) {
      return text.replace(/\n/g, "<br>");
    } else {
      return text;
    }
  };
  // 列表数据
  var getTableList = [];
  //审核失败数据
  var checkFailReasonArr = [];
  // 审核失败操作按钮按钮
  var checkFailReasonDialog = function (lineData) {
    Widgets.dialog.html(
      "选择关键字",
      function () {
        return `<div>
        <div>
          <label>驳回类型</label>
          <select name="pets" id="pet-select">
            <option value="">请选择驳回类型</option>
            <option value="1">涉嫌辱骂</option>
            <option value="2">疑似广告</option>
            <option value="3">涉及个人隐私</option>
            <option value="4">点评内容无意义</option>
            <option value="5">涉嫌涉政</option>
          </select>
        </div>
        <div>
          <label>具体原因</label>
          <textarea id="failReason" placeholder="输入具体原因" cols="50" />
        </div>
      </div>`;
      },
      {
        width: 400,
        buttons: [
          {
            name: "确认",
            xtype: "success",
            click: function () {
              const that = this;
              const failReason = $("#failReason").val().trim();
              const petSelectReason = $("#pet-select").val().trim();
              var index = checkFailReasonArr.findIndex(
                (item) => item.appendReviewId === lineData.id
              );
              // 防止数据重复添加
              if (index > -1) {
                checkFailReasonArr = checkFailReasonArr.splice(1, index);
              }
              checkFailReasonArr.push({
                appendReviewId: lineData.id,
                failType: petSelectReason,
                failReason: failReason,
              });
              setTimeout(function () {
                that.close();
              }, 200);
            },
          },
          {
            name: "关闭",
            xtype: "primary",
            click: function () {
              this.close();
            },
          },
        ],
      }
    );
  };
  let isAudit = function (Tables, paramStr, type) {
    // 筛选选中的数据
    let hasRisk = getTableList.filter(
      (item) => paramStr.indexOf(item.id + "") !== -1
    );
    var reductArray = []; // 二次选中的数据id
    Widgets.dialog
      .html(type == "pass" ? "请确认 - 审核通过" : "请确认 - 审核失败", {
        width: 700,
        buttons: [
          {
            name: type == "pass" ? "审核通过" : "审核失败",
            xtype: "primary",
            click: function () {
              // 默认全选
              var getSelectRadio = document.getElementsByClassName(
                "monidianjuimonidianjui"
              );
              $(getSelectRadio).click();

              // 审核通过
              let shieldContentArr = [];
              if (reductArray.length === 0) {
                Widgets.dialog.alert("请勾选数据");
                return;
              } else {
                reductArray.forEach((item) => {
                  shieldContentArr.push({
                    appendReviewId: item,
                  });
                });
              }
              this.close();
              if (type == "pass") {
                Simple.Store2(
                  "dianping!dianping-follow-up/data/audit",
                  {
                    status: 1,
                    editList: JSON.stringify(shieldContentArr),
                  },
                  "save"
                ).then(
                  function () {
                    Tables.render();
                  },
                  function (data) {
                    Widgets.dialog.alert(data.message);
                  }
                );
              } else {
                Simple.Store2(
                  "dianping!dianping-follow-up/data/audit",
                  {
                    status: 4,
                    editList: JSON.stringify(checkFailReasonArr),
                  },
                  "save"
                ).then(
                  function () {
                    Tables.render();
                    checkFailReasonArr = [];
                  },
                  function (data) {
                    Widgets.dialog.alert(data.message);
                    checkFailReasonArr = [];
                  }
                );
              }
            },
          },
          {
            name: "取消",
            xtype: "warning",
            click: function () {
              this.close();
              // 清空数据
              checkFailReasonArr = [];
            },
          },
        ],
      })
      .done(function (dialog) {
        Table(
          {
            width: 800,
            selector: {
              dataIndex: "id",
            },
            buttons: {
              top: [
                {
                  name: "",
                  class: "primary monidianjuimonidianjui",
                  click: function (table, dom, config, arr) {
                    reductArray = arr;
                  },
                },
              ],
            },
            columns: [
              {
                header: "#",
                dataIndex: "id",
                width: 80,
              },
              {
                header: "内容",
                dataIndex: "content",
                width: 500,
                render: function (data) {
                  return '<div style="max-width: 450px;">' + data + "</div>";
                },
              },
            ],
            operations: [
              {
                name: type === "fail" ? "操作" : "",
                class: "info",
                click: function (table, row, lineData) {
                  checkFailReasonDialog(lineData);
                },
              },
            ],
          },
          { data: hasRisk },
          $(dialog.body),
          function (target, table, item) {
            let checkbox = item("table-selector");
            let checkboxHeader = item("table-header-selector");
            $(checkbox).css({ width: "20px", height: "20px" });
            $(checkboxHeader).css({ width: "20px", height: "20px" });
            if (hasRisk.length > 0) {
              $(checkbox).attr("checked", true);
              $(checkboxHeader).attr("checked", true);
            }
          }
        ).render();
      });
  };
  // 审核通过
  var letPass = function (Tables, paramStr) {
    if (paramStr.length <= 0) {
      Widgets.dialog.alert("请勾选数据");
      return;
    }
    isAudit(Tables, paramStr, "pass");
  };
  // 审核失败
  var letFail = function (Tables, paramStr) {
    if (paramStr.length <= 0) {
      Widgets.dialog.alert("请勾选数据");
      return;
    }
    isAudit(Tables, paramStr, "fail");
  };

  // 待审核列表
  var list = function (panel) {
    Table(
      {
        description: "待审核追评",
        title: "待审核追评",
        search: [
          {
            xtype: "text",
            dataIndex: "id",
            placeholder: "请输入id",
          },
          {
            xtype: Plugin("simple!select-district", {
              name: "cityCode",
              insert: {
                province: [
                  {
                    code: "",
                    name: "请选择省份",
                  },
                ],
                city: [
                  {
                    code: "",
                    name: "请选择市",
                  },
                ],
              },
            }),
            dataIndex: "cityCode",
          },
          {
            xtype: "select",
            dataIndex: "placeToken",
            autoSubmit: true,
            value: "驾校点评",
            store: searchTokenStore,
          },
          {
            xtype: "number",
            dataIndex: "topic",
            placeholder: "请输入驾校id或教练id",
          },
          {
            xtype: "text",
            dataIndex: "authorId",
            placeholder: "请输入用户木仓id",
          },
          {
            xtype: "select",
            dataIndex: "abnormal",
            autoSubmit: true,
            store: [
              {
                key: "",
                value: "请选择主体异常新增",
              },
              {
                key: true,
                value: "是",
              },
              {
                key: false,
                value: "否",
              },
            ],
          },
          {
            xtype: "text",
            dataIndex: "startVersion",
            placeholder: "APP起始版本",
          },
          {
            xtype: "text",
            dataIndex: "endVersion",
            placeholder: "APP结束版本",
          },
        ],
        buttons: {
          top: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "审核通过",
              class: "primary",
              click: function (table, dom, config, arr) {
                letPass(table, arr);
              },
            },
            {
              name: "审核失败",
              class: "warning",
              click: function (table, dom, config, arr) {
                letFail(table, arr);
              },
            },
          ],
          bottom: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
          ],
        },
        selector: {
          dataIndex: "id",
        },
        columns: [
          {
            header: "追评审核ID",
            dataIndex: "id",
            width: 20,
          },
          {
            header: "主体ID",
            dataIndex: "topic",
            render: function (data, table, row) {
              var name = "";
              searchTokenStore.forEach((item) => {
                if (item.key === row.placeToken) {
                  name = item.value;
                }
              });
              let html = `<p>${name}: ${data}</p>`;
              return html;
            },
            width: 150,
          },
          {
            header: "主体名称",
            dataIndex: "topicName",
            width: 150,
          },
          {
            header: "主体异常新增",
            dataIndex: "abnormal",
            render: function (data) {
              var bool = data ? "是" : "否";
              return (
                '<div><span style="color:' +
                (data ? "red" : "#33333") +
                '">' +
                bool +
                "</div>"
              );
            },
            width: 150,
          },
          {
            header: "用户",
            dataIndex: "userinfoResponse",
            render: function (data) {
              if(!data) {
                return
              }
              return (
                '<div><img src="' +
                data.avatar +
                '"style="max-width:50px; max-height:50px;border-radius: 25px;" />' +
                "<br/>" +
                data.nickname +
                "</div>"
              );
            },
          },
          {
            header: "最新可信度",
            width: 100,
            dataIndex: "credibility",
            render: (data, arr, lineData) =>
              data === 0
                ? lineData.registerSameDay
                  ? `未知：${lineData.registerSameDay}`
                  : "未知"
                : data === 1
                ? "低"
                : data === 2
                ? "高"
                : "数据暂未同步",
          },
          {
            header: "可信度更新时间",
            width: 100,
            dataIndex: "credibilityUpdateTime",
            render: (data, arr, lineData) => {
              if (data) {
                return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
              }
            },
          },
          {
            header: "最初可信度",
            width: 100,
            dataIndex: "originalCredibility",
            render: (data, arr, lineData) => {
              if (!lineData.credibilityUpdateTime) {
                return "";
              } else {
                switch (data) {
                  case 1: {
                    return "低";
                  }
                  case 2: {
                    return "高";
                  }
                  default: {
                    return "未知";
                  }
                }
              }
            },
          },
          {
            header: "内容",
            dataIndex: "originalContent",
            width: 500,
            render: function (data, arrData, lineData, index) {
              const title = lineData.originalContent
                ? lineData.originalContent
                : lineData.content;
              return (
                '<div style="max-width: 450px;">' + regBrText(title) + "</div>"
              );
            },
          },
          {
            header: "关联点评ID",
            dataIndex: "dianpingId",
          },
          {
            header: "追评属性",
            dataIndex: "score",
            render: function (data, allData, lineData) {
              let html = `<p>APP版本:${lineData.version}</p>
                  <p>IP属地:${lineData.ipLocation}</p>
                  <p>驾校省份:${lineData.cityName}</p>
                `;
              return html;
            },
            width: 300,
          },
          {
            header: "创建时间",
            dataIndex: "createTime",
            render: function (data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            },
          },
        ],
      },
      [
        "dianping!dianping-follow-up/data/list?placeToken=" +
          searchTokenStore[0].key,
      ],
      panel,
      function (ele, dataList, item) {
        getTableList = dataList.data;
        let checkbox = item("table-selector");
        let checkboxHeader = item("table-header-selector");
        $(checkbox).css({ width: "20px", height: "20px" });
        $(checkboxHeader).css({ width: "20px", height: "20px" });
      }
    ).render();
  };

  return {
    list,
  };
});
