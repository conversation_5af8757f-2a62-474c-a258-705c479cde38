﻿"use strict";

define(function () {
  var list = {
    load: {
      url: "dianping://api/admin/dianping-append-review/list.htm",
      type: "get",
      format: function (data) {
        data.map(function (item, index) {
          if (item.content && (item.content.search(/<script.*>/gi) > -1)) {
            item.content = item.content.replace(/<script.*>?/gi, "");
          }

          if (item.content && (item.content.search(/<\/script>/gi) > -1)) {
            item.content = item.content.replace(/<\/script>/gi, "");
          }

          return item;
        });

        return data;
      },
    },
  };

  var audit = {
    primary: "id",
    save: {
      url: "dianping://api/admin/dianping-append-review/audit.htm",
      type: "post",
    },
  };

  return {
    list,
    audit
  };
});
