"use strict";

define(['simple!core/plugin', 'simple!core/utils', 'simple!core/store', 'simple!core/widgets', 'simple!core/table'], function (Plugin, Utils, Store, Widgets, Table) {

    var renderClub = function (id, callback) {
        Widgets.dialog.html('选择车友会', { width: 600, height: 600 }).done(function (dialog) {
            Table({
                title: '车友会列表',
                description: '车友会列表',
                search: [
                    {
                        xtype: 'text',
                        dataIndex: 'name',
                        placeholder: '车友会名称',
                        value: name
                    }
                ],
                operations: [
                    {
                        name: '选择',
                        class: 'danger',
                        click: function (table, row, lineData) {
                            callback(lineData);
                            dialog.close();
                        }
                    }
                ],
                columns: [
                    {
                        header: '#',
                        dataIndex: 'clubId'
                    },
                    {
                        header: '车友会名称',
                        dataIndex: 'name'
                    }
                ]
            }, ['dianping!car-club/data/club?categoryId=' + id], dialog.body).render({
                limit: 10
            });
        });
    };
    var columnStoreData = []
    var columns = [
        {
            header: '日期',
            dataIndex: 'date'
        },
        {
            header: '教练点评',
            dataIndex: '07c6d1dc5912405580584c2d02952036'
        },

    ]
    var chartConfigData = []
    var setSeriesData = function(data) {
        var tempArr = [];
        for(let i = 0; i < columnStoreData.length; i++) {
            for(let j = 0; j < data.y.length; j++) {
                // 已存在
                var index = chartConfigData.findIndex(item=>item.key===columnStoreData[i].dataIndex);
                if(index>-1) {
                    chartConfigData[index].data.push(data.y[j][columnStoreData[i].dataIndex])
                } else {
                    tempArr = []
                    tempArr.push(data.y[j][columnStoreData[i].dataIndex])
                    chartConfigData.push({
                        key:columnStoreData[i].dataIndex,
                        data: tempArr,
                        name:columnStoreData[i].header
                    })
                }
            }
        }
        console.log(chartConfigData, 'chartConfigData')
        return chartConfigData || []
    }
    // 获取动态列
    var getColumn = function (){
        Store(['dianping!stat/data/column']).load([
            {
                aliases:'list',
                params: {
                    fromPathType: 4
                }
            }
        ]).done(function(store, data){
                if(data.list.status==='success'){
                    columnStoreData = data.list.data;
                    columns = [...columns, ...columnStoreData];
                }
            })
    }
    var list1 = function (panel) {
        var categoryValue = '';
        var clubName = '';
        var clubId = '';
        var clubWraper = $('<div class="form-group"></div>');
        getColumn();
        setTimeout(function(){
            Plugin('dianping!report', {
                store: 'dianping!stat/data/list',
                success: function(obj, data) {
                    chartConfigData = []
                    // 覆盖初始化数据
                    this.charts.series = setSeriesData(data.report.data);
                },
                title: '每日新增',
                description: '每日新增',
                target: panel,
                params: {
                    top: 7,
                    placeToken: '07c6d1dc5912405580584c2d02952036'
                },
                search: [
                    {
                        xtype: 'report-period',
                        dataIndex: ['dayFrom', 'dayTo'],
                        selector: [
                            {
                                key: 365,
                                value: '全部',
                                class: 'danger'
                            },
                            {
                                key: 30,
                                value: '最近30天',
                            },
                            {
                                key: 7,
                                value: '最近7天',
                                default: true
                            },
                            {
                                key: 1,
                                value: '最近1天'
                            }
                        ]
                    },
                    {
                        xtype: 'text',
                        dataIndex: 'platform',
                        placeholder: '平台'
                    },
                ],
                buttons: [
                    {
                        name: '全部',
                        class: 'danger',
                        click: function (e, config, render) {
                            config.params = {
                                top: 365,
                                placeToken: '07c6d1dc5912405580584c2d02952036'
                            }
                            render(config);
                        }
                    },
                    {
                        name: 'TOP30',
                        click: function (e, config, render) {
                            config.params = {
                                top: 30,
                                placeToken: '07c6d1dc5912405580584c2d02952036'
                            }
                            render(config);
                        }
                    },
                    {
                        name: 'TOP20',
                        click: function (e, config, render) {
                            config.params = {
                                top: 20,
                                placeToken: '07c6d1dc5912405580584c2d02952036'
                            }
                            render(config);
                        }
                    },
                    {
                        name: 'TOP10',
                        click: function (e, config, render) {
                            config.params = {
                                top: 10,
                                placeToken: '07c6d1dc5912405580584c2d02952036'
                            }
                            render(config);
                        }
                    }
                ],
                format: {
                    chart: {
                        group: {
                            xAxis: 'title',
                            yAxis: 'date'
                        },
                        label: {
                            key: 'title',
                            value: 'title',
                            name: {
                                /*topic: '话题数',
                                 comment: '回复数',
                                 vote: '投票数',
                                 zan: '点赞数'*/
                            }
                        },
                        xAxis: 'title',
                        yAxis: 'count'
                    },
                    table: { //  这是format对象
                        label: 'id',
                        count: 'count',
                        group: 'date'
                    }
                },
                charts: {
                    series: []
                },
                table: {
                    columns: columns
                }
            }, function (plugin, report) {
                clubWraper.html('');
                var dom = plugin.config.target;
                var $categoryId = dom.item('categoryId');
                $categoryId.html('<option value="">选择分组</option>');
                var clubSel = $('<div class="btn btn-success">'+(clubName?clubName: '选择车友会')+'</div>').appendTo(clubWraper);
                var $clubId = $('<input type="hidden" name="clubId" value="'+(clubId?clubId:'')+'" data-item="clubId">').appendTo(clubWraper);
                dom.item('categoryId-group').after(clubWraper);
            }).render();
        },200)
    };

    var list2 = function (panel) {
        var categoryValue = '';
        var clubName = '';
        var clubId = '';
        var clubWraper = $('<div class="form-group"></div>');
        Plugin('dianping!report', {
            store: 'dianping!stat/data/list2',
            title: '管理日志',
            description: '管理日志',
            target: panel,
            params: { top : 7 },
            search: [
                {
                    xtype: 'report-period',
                    dataIndex: ['dayFrom', 'dayTo'],
                    selector: [
                        {
                            key: 365,
                            value: '全部',
                            class: 'danger'
                        },
                        {
                            key: 30,
                            value: '最近30天',
                        },
                        {
                            key: 7,
                            value: '最近7天',
                            default: true
                        },
                        {
                            key: 1,
                            value: '最近1天'
                        }
                    ]
                },
            ],
            buttons: [
                {
                    name: '全部',
                    class: 'danger',
                    click: function (e, config, render) {
                        config.params = {
                            top: 365
                        }
                        render(config);
                    }
                },
                {
                    name: 'TOP30',
                    click: function (e, config, render) {
                        config.params = {
                            top: 30
                        }
                        render(config);
                    }
                },
                {
                    name: 'TOP20',
                    click: function (e, config, render) {
                        config.params = {
                            top: 20
                        }
                        render(config);
                    }
                },
                {
                    name: 'TOP10',
                    click: function (e, config, render) {
                        config.params = {
                            top: 10
                        }
                        render(config);
                    }
                }
            ],
            format: {
                chart: {
                    group: {
                        xAxis: 'title',
                        yAxis: 'date'
                    },
                    label: {
                        key: 'title',
                        value: 'title',
                        name: {
                            /*topic: '话题数',
                             comment: '回复数',
                             vote: '投票数',
                             zan: '点赞数'*/
                        }
                    },
                    xAxis: 'title',
                    yAxis: 'count'
                },
                table: { //  这是format对象
                    label: 'id',
                    count: 'count',
                    group: 'date'
                }
            },
            table: {
                columns: [

                    {
                        header: '日期',
                        dataIndex: 'date'
                    },

                    {
                        header: '高尤波',
                        dataIndex: '16'
                    },
                    {
                        header: '李琦',
                        dataIndex: '242'
                    },
                    {
                        header: '唐成策',
                        dataIndex: '320'
                    },
                    {
                        header: '孙雪娟',
                        dataIndex: '孙雪娟'
                    },
                    {
                        header: '饶维婕',
                        dataIndex: '饶维婕'
                    },
                    {
                        header: '杨宇涵',
                        dataIndex: '384'
                    },
                ]
            }
        }, function (plugin, report) {
            clubWraper.html('');
            var dom = plugin.config.target;
            var $categoryId = dom.item('categoryId');
            $categoryId.html('<option value="">选择分组</option>');
            var clubSel = $('<div class="btn btn-success">'+(clubName?clubName: '选择车友会')+'</div>').appendTo(clubWraper);
            var $clubId = $('<input type="hidden" name="clubId" value="'+(clubId?clubId:'')+'" data-item="clubId">').appendTo(clubWraper);
            dom.item('categoryId-group').after(clubWraper);
        }).render();
    };


    return {
        list1 : list1,
        list2: list2
    }

});