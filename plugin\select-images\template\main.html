﻿<!--
    * main v1.0
    *
    * name: GG
    * date: 2013/12/25
-->

<script id="cheyouquan-plugin-select-images-template-main-btn" type="text/html" >

    <style>

        .plugin-select-images button {
            position: relative;
        }

        .plugin-select-images button input {
            width: 100%;
            height: 100%;
            position: absolute; 
            top: 0; 
            left: 0;
            opacity: 0;
        }

        .plugin-select-images ul li {
            width: 120px;
            height: 90px;
            float: left;
            margin: 10px 10px 0 0;
            overflow: hidden;
            position: relative;
            border: 1px solid #808080;
        }

        .plugin-select-images ul li p {
            line-height: 90px;
        }

        .plugin-select-images ul li p img {
            width: 100%;
        }

        .plugin-select-images ul li span {
            line-height: 90px;
            width: 120px;
            text-align: center;
            color: #000;
            position: absolute;
            top: 0;
            left: 0;
        }

        .plugin-select-images ul li > input {
            position: absolute;
            bottom: 0;
            left: 0;
            border: 0;
            width: 120px;
            box-sizing: border-box;
            padding: 2px;
        }

        .plugin-select-images ul li > .close {
            position: absolute;
            top: 3px;
            right: 3px;
        }

    </style>

    <div class="plugin-select-images">
        <button type="button" class="btn btn-info">
            <i>选择图片</i>
            <input type="file" accept="image/gif, image/jpeg, image/jpg, image/png" <? if(config.multiple){ ?>multiple<? } ?> />
        </button>
        <ul data-item="plugin-image-list" class="clearfix">
            <? for(var i = 0 ; i < data.length; i++){ ?>
                <li data-success="true">
                    <p><img src="<?=data[i][config.imageSrc.dataIndex] ?>"></p>
                    <span></span>
                    <? if(config.imageText){ ?>
                        <input type="text" placeholder="<?=config.imageText.placeholder ?>" value="<?=data[i][config.imageText.dataIndex] ?>" />
                    <? } ?>
                    <i type="button" class="close">&times;</i>
                </li>
            <? } ?>
        </ul>
        <p data-item="upload-images-size" style="color:#ff0000"></p>
        <input type="hidden" data-item="plugin-image-input" name="<?=config.dataIndex || config.column.dataIndex ?>" value="<?=config.value ?>" />
    </div>
</script>