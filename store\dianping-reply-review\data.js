﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'dianping://api/admin/dianping-reply/list-review.htm',
            type: 'get',
            format: function (data) {

                data.map(function (item, index) {
                    if (item.content && (item.content.search(/<script.*>/gi) > -1)) {
                        item.content = item.content.replace(/<script.*>?/gi, '');
                    }

                    if (item.content && (item.content.search(/<\/script>/gi) > -1)) {
                        item.content = item.content.replace(/<\/script>/gi, '');
                    }
                    return item;
                });
                return data;
            }
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/dianping-reply-review/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'dianping://api/admin/dianping-reply-review/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/dianping-reply-review/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'dianping://api/admin/dianping-reply-review/view.htm',
            type: 'get'
        }
    }

    var audit = {
        save: {
            url: 'dianping://api/admin/dianping-reply/audit.htm',
            type: 'post'
        }
    }
    return {
        list: list,
        audit: audit,
        delete: del,
        update: update,
        insert: insert,
        view: view
    }

});