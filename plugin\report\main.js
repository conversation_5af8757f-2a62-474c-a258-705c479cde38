/*
 * product-filter v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
*/

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/store', 'simple!core/form', 'simple!core/utils'], function (Template, Table, Store, Form, Utils) {

    var report = function (plugin, success) {

        this.plugin = plugin;
        this.success = success;

        var me = this;
        // 加载控件
        require([me.plugin.path + '/resources/lib/highcharts/highcharts'], function () {

            // 默认配置
            plugin.config.search = plugin.config.search || [{
                xtype: 'report-period',
                dataIndex: ['fromTime', 'toTime'],
                selector: [
                    {
                        key: 365,
                        value: '全部',
                        class: 'danger'
                    },
                    {
                        key: 30,
                        value: '最近30天'
                    },
                    {
                        key: 7,
                        value: '最近7天',
                        default: true
                    },
                    {
                        key: 1,
                        value: '昨天'
                    },
                    {
                        key: 0,
                        value: '今天'
                    }
                ]
            }];

            plugin.config.buttons = plugin.config.buttons || [
                {
                    name: '全部',
                    class: 'danger',
                    click: function (e, config, render) {
                        config.params = {
                            top: 365
                        }
                        me.render(config);
                    }
                },
                {
                    name: 'TOP30',
                    click: function (e, config, render) {
                        config.params = {
                            top: 30
                        }
                        me.render(config);
                    }
                },
                {
                    name: 'TOP20',
                    click: function (e, config, render) {
                        config.params = {
                            top: 20
                        }
                        me.render(config);
                    }
                },
                {
                    name: 'TOP10',
                    click: function (e, config, render) {
                        config.params = {
                            top: 10
                        }
                        me.render(config);
                    }
                }
            ];

            plugin.config.params = plugin.config.params || { top: 30 };

            for (var i = 0; i < plugin.config.search.length; i++) {
                if (plugin.config.search[i].xtype === 'report-period') {
                    plugin.config.search[i].xtype = 'dianping-plugin-report-template-main-period';
                    plugin.config.search[i].item = 'report-period';
                }
            }

            Template(me.plugin.path + '/template/main/index', plugin.config.target, function (dom, data, item) {

                // 绑定按钮事件
                me.bindOperation(item, plugin.config);

            }, {
                config: plugin.config
            }).render();

        });

    }

    report.prototype = {

        // 渲染内容
        render: function (item, config) {

            var me = this;
            // 渲染表格
            if (config.table) {
                Table(config.table, [config.store], item('table'), function () {
                }).render(config.params, {
                    pagingType: 'local',
                    loadData: false,
                    load: {
                        format: function (data) {
                            data = data.y;
                            var format = config.format && config.format.table;
                            if (format) {
                                var group = {}, value = [];
                                for (var i = 0; i < data.length; i++) {
                                    group[data[i][format.group]] = group[data[i][format.group]] || [];
                                    group[data[i][format.group]].push(data[i]);
                                }
                                for (var e in group) {
                                    var index = value.push({}) - 1;
                                    for (var i = 0; i < group[e].length; i++) {
                                        for (var n in group[e][i]) {
                                            if (n !== format.label && n !== format.count) {
                                                value[index][n] = group[e][i][n];
                                            } else {
                                                value[index][group[e][i][format.label]] = group[e][i][format.count];
                                            }
                                        }
                                    }
                                }
                            } else {
                                value = data;
                            }
                            return value;
                        }
                    }
                }).done(function () {
                    // 渲染图表
                    if(!config.table.only){

                        me.renderChart(config, item('charts'));
                    }else{
                        $('div.loader').hide();
                    }
                    
                });
            } else {

                // 渲染图表
                me.renderChart(config, item('charts'));

            }

        },

        // 绑定搜索事件
        bindOperation: function (item, config) {

            var me = this;

            var form = item('search');
            var preiod = item('report-period-group');
            var buttons = item('buttons').find('button');

            // top
            buttons.on('click', function (e) {
                var func = config.buttons[$(this).attr('data-index')].click || $.noop;
                func.call(this, e, config, function (userConfig) {
                    $.extend(true, config, userConfig);
                    if (form.size() > 0) {
                        form.submit();
                    } else {
                        me.render(item, config);
                    }
                }, buttons);
            });

            // form
            if (form.size() > 0) {
                Form.bind(form, {
                    submitHandler: function (form) {
                        $.extend(config.params, Form.input(form).data);
                        me.render(item, config);
                        return false;
                    }
                });

                if (preiod.size() > 0) {
                    preiod.item('preiod-selector').on('click', function () {
                        preiod.item('preiod-selector').prop('disabled', false);
                        $(this).prop('disabled', true);
                        var value = $(this).val();
                        var fromDate = new Date();
                        preiod.item('from').val(Utils.format.date(fromDate - (value * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'));
//                        if(value > 0){
//                            preiod.item('to').val(Utils.format.date(fromDate - (24 * 60 * 60 * 1000), 'yyyy-MM-dd'));
//                        }else{
                            preiod.item('to').val(fromDate.format('yyyy-MM-dd'));
//                        }
                        form.submit();
                    }).filter('[data-default]').eq(0).trigger('click');
                } else {
                    form.submit();
                }
            } else {
                me.render(item, config);
            }

        },

        // 渲染图表
        renderChart: function (config, target) {

            var me = this;

            Store([config.store]).load([{
                aliases: 'report',
                params: config.params
            }]).done(function (obj, data) {
                config.success && config.success(obj, data)
                // 图表默认配置
                var chartConfig = {
                    chart: {
                        type: 'line',
                        height: '500'
                    },
                    credits: {
                        enabled: false
                    },
                    title: {
                        text: ''
                    },
                    xAxis: {
                        type: 'datetime',
//                        categories: [],
                        labels: {
                            rotation: 45,
                            align: 'left',
                            style: {
                                fontSize: '14px',
                                fontFamily: 'Verdana, sans-serif'
                            }
                        }
                    },
                    yAxis: {
                        title: {
                            text: ''
                        }
                    },
                    tooltip: {
                        formatter: function () {
                            return '<b>' + this.series.name + '</b><br/>' +
                                this.x + ': <b>' + this.y + '</b>';
                        }
                    }
                }

                config.charts = config.charts || {};

                $.extend(true, chartConfig, config.charts);
                 // 业务自行传渲染数据
                if( config.charts.series) {
                    chartConfig.series = config.charts.series;
                    chartConfig.xAxis.categories = me.formatxAxis(config, data.report.data.x);
                } else {
                    if(config.charts.continuous === false){
                        chartConfig.series = me.formatyAxis2(config, data.report.data);
                    }else{
                        chartConfig.series = me.formatyAxis(config, data.report.data);
                        chartConfig.xAxis.categories = me.formatxAxis(config, data.report.data.x);
                    }
                }
                console.log(chartConfig)
//                console.log(chartConfig.series, chartConfig.xAxis)
                $(target).highcharts(chartConfig);

                me.success(me);

            });

        },

        // 格式化x轴数据
        formatxAxis: function (config, data) {

            var xAxis = [];

            for (var i = 0; i < data.length; i++) {
                xAxis.push(data[i][config.format.chart.xAxis]);
            }

            return xAxis;

        },

        formatyAxis2: function (config, data) {
            var y = data.y;
            var x = data.x;
            var format = config.format.chart;
            var label = {};
            var group = {};
            var groupValue = [];
            for (var i = 0; i < x.length; i++) {
                for (var j = 0; j < y.length; j++) {
                    group[y[j][format.label.key]] = group[y[j][format.label.key]] || {
                        data: [],
                        other: []
                    };
                    label[y[j][format.label.key]] = y[j][format.label.value];
                    if (x[i][format.group.xAxis] === y[j][format.group.yAxis]) {
                        group[y[j][format.label.key]].data.push([new Date(y[j][format.group.yAxis]).valueOf(), y[j][format.yAxis]]);
                        group[y[j][format.label.key]].other.push(y[j]);
                        y.splice(j, 1);
                        j--;
                    }
                }
            }
            for (var e in group) {
                groupValue.push({
                    name: (format.label.name && format.label.name[label[e]]) || label[e],
                    data: group[e].data,
                    other: group[e].other
                });
            }
console.log(groupValue)
            return groupValue;
        },

        // 格式化y轴数据
        formatyAxis: function (config, data) {

            var y = data.y;
            var x = data.x;
            var format = config.format.chart;

            var label = {};
            var group = {};
            var groupValue = [];

            for (var i = 0; i < x.length; i++) {
                for (var j = 0; j < y.length; j++) {
                    group[y[j][format.label.key]] = group[y[j][format.label.key]] || {
                        data: [],
                        other: []
                    };
                    label[y[j][format.label.key]] = y[j][format.label.value];
                    if (x[i][format.group.xAxis] === y[j][format.group.yAxis]) {
                        group[y[j][format.label.key]].data.push(y[j][format.yAxis]);
                        group[y[j][format.label.key]].other.push(y[j]);
                        y.splice(j, 1);
                        j--;
                    }
                }
            }

            for (var e in group) {
                groupValue.push({
                    name: (format.label.name && format.label.name[label[e]]) || label[e],
                    data: group[e].data,
                    other: group[e].other
                });
            }

            return groupValue;

        }

    }

    report.prototype.constructor = report;

    return function (plugin, success) {
        return new report(plugin, success);
    }

});