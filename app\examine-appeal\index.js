/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */
"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

  const AppealType = {
    // 通过
    PASS: 3,
    REJECT: 2
  }

  const APPEAL_REASON = {
    1: '无故差评',
    2: '非本校学员',
    3: '同行不正当竞争',
    4: '恶意推广',
    5: '其他',
    6: '无法联系学员'
  }

  var list = function (panel) {
    Table({
      description: '待审核申诉',
      title: '待审核申诉',
      search: [
        {
          xtype: 'text',
          dataIndex: 'dianpingId',
          placeholder: '请输入id'
        },
        {
          xtype: 'text',
          dataIndex: 'topic',
          placeholder: '请输入topic'
        },
        {
          xtype: 'text',
          dataIndex: 'authorId',
          placeholder: '请输入authorId'
        },
      ],
      buttons: {
        top: [
          {
            name: '刷新',
            class: 'info',
            click: function (obj) {
              obj.render();
            }
          }
        ],
        bottom: [
          {
            name: '刷新',
            class: 'info',
            click: function (obj) {
              obj.render();
            }
          }
        ]
      },
      operations: [
        {
          name: '查看',
          xtype: 'view',
          width: 400,
          class: 'success',
          title: '查看',
          store: 'dianping!examine-appeal/data/view',
          columns: [
            {
              header: '主题信息：',
              dataIndex: 'topic',
              render: function (data, arrData, lineData, index) {
                return (lineData.placeName || '') + "<br/>" + (lineData.topic || '')
              },
            },
            {
              header: "最新可信度：",
              width: 100,
              dataIndex: "credibility",
              render: (data, arr, lineData) =>
                data === 0
                  ? (lineData.registerSameDay ? `未知：${lineData.registerSameDay}` : '未知')
                  : data === 1
                  ? "低"
                  : data === 2
                  ? "高"
                  : "数据暂未同步",
            },
            {
              header: "可信度更新时间：",
              dataIndex: "credibilityUpdateTime",
              render: (data, arr, lineData) =>{
                if(data) {
                  return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                }
              }
            },
            {
              header: "最初可信度：",
              width: 100,
              dataIndex: "originalCredibility",
              render: (data, arr, lineData) =>{
                if(!lineData.credibilityUpdateTime){
                  return ''
                } else {
                  switch(data){
                    case 1: {
                      return '低';
                    }
                    case 2: {
                      return '高';
                    }
                    default: {
                      return '未知';
                    }
                  }
                }
              }
            },
            {
              header: '申诉原因：',
              dataIndex: 'complainType',
              render: function(data) {
                return APPEAL_REASON[data] || ''
              }
            },
            {
              header: '提交时间：',
              dataIndex: 'createTime',
              render: function (data) {
                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
              }
            }
          ]
        },
        {
          name: '申诉通过',
          class: 'info',
          click: function (table, lineDom, lineData) {
            Widgets.dialog.html('确定申诉通过并删除该评价及回复吗？','<textarea id="failReason" placeholder="输入通过原因，非必填" cols="50" />', {
              width: 400,
              buttons: [{
                name: '确认',
                xtype: 'success',
                click: function () {
                  const that = this;
                  const failReason = $('#failReason').val().trim();
                  Simple.Store2('dianping!examine-appeal/data/update', {
                    ids: lineData.dianpingId,
                    status: AppealType.PASS,
                    reason: failReason
                  }, 'save').then(function () {
                      that.close();
                      table.render();
                    },function (data) {
                      Widgets.dialog.alert(data.message);
                    });
                }
            }, {
                name: '关闭',
                xtype: 'primary',
                click: function () {
                    this.close();
                }
            }]
            })
          }
        },
        {
          name: '申诉失败',
          class: 'danger',
          click: function (table, lineDom, lineData) {
            Widgets.dialog.html('确定申诉失败吗？','<textarea id="failReason" placeholder="输入失败原因，非必填" cols="50" />', {
              width: 400,
              buttons: [{
                  name: '确认',
                  xtype: 'success',
                  click: function () {
                    const failReason = $('#failReason').val().trim();
                    const that = this;
                    Simple.Store2('dianping!examine-appeal/data/update', {
                      ids: lineData.dianpingId,
                      status: AppealType.REJECT,
                      reason: failReason
                    }, 'save').then(function () {
                        table.render();
                        that.close();
                      },function (data) {
                        Widgets.dialog.alert(data.message);
                      });
                  }
              }, {
                  name: '关闭',
                  xtype: 'primary',
                  click: function () {
                      this.close();
                  }
              }]
            }).done(function (dialog) {
            })
          }
        }
      ],
      columns: [
        {
          header: '编号',
          dataIndex: 'id',
          width: 20
        },
        {
          header: '主题信息',
          dataIndex: 'topic',
          width: 150,
          render: function (data, arrData, lineData, index) {
            return (lineData.placeName || '') + "<br/>" + (lineData.topic || '')
          },
        },
        {
          header: "最新可信度",
          width: 100,
          dataIndex: "credibility",
          render: (data, arr, lineData) =>
            data === 0
              ? (lineData.registerSameDay ? `未知：${lineData.registerSameDay}` : '未知')
              : data === 1
              ? "低"
              : data === 2
              ? "高"
              : "数据暂未同步",
        },
        {
          header: "可信度更新时间",
          width: 100,
          dataIndex: "credibilityUpdateTime",
          render: (data, arr, lineData) =>{
            if(data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            }
          }
        },
        {
          header: "最初可信度",
          width: 100,
          dataIndex: "originalCredibility",
          render: (data, arr, lineData) =>{
            if(!lineData.credibilityUpdateTime){
              return ''
            } else {
              switch(data){
                case 1: {
                  return '低';
                }
                case 2: {
                  return '高';
                }
                default: {
                  return '未知';
                }
              }
            }
          }
        },
        {
          header: "学员评分",
          dataIndex: "score",
          width: 90,
        },
        {
          header: '原点评内容',
          dataIndex: 'originalContent',
          width: 200,
          render: function (data, arrData, lineData, index) {
            return lineData.originalContent ? lineData.originalContent : lineData.content
          }
        },
        {
          header: '原点评图片',
          dataIndex: "dianpingImageList",
          width: 100,
          render: function (data, arrData, lineData, index) {
            return lineData.dianpingImageList && lineData.dianpingImageList.length > 0
              ? "<a>共" + lineData.dianpingImageList.length + "张图片</a>"
              : "";
          },
          click: function (table, row, lineData) {
            var str = "";
            var count = 0;
            for (var i = 0; i < lineData.dianpingImageList.length; i++) {
              count++;
              str +=
                '<img style="" src="' +
                lineData.dianpingImageList[i].url +
                '!max800" width="300" height="300">' +
                "第" +
                count +
                "张";
            }
            Widgets.dialog.html("共" + lineData.dianpingImageList.length + "张图片", str, {
              width: 800,
            });
          },
        },
        {
          header: '原回复内容',
          dataIndex: 'replyContent',
          width: 200
        },
        {
          header: '申诉原因',
          dataIndex: 'complainType',
          render: function(data) {
            return APPEAL_REASON[data] || ''
          }
        },
        {
          header: '申诉详情',
          dataIndex: 'complainContent'
        },
        {
          header: '申诉材料',
          dataIndex: 'imageCount',
          width: 100,
          render: function (data, arrData, lineData, index) {
            var str = ''
            lineData.imageCount > 0
            ? str = "共" + lineData.imageCount + "张图片"
            : "";
            if (lineData.videoUrl) {
              str+= "<br/>1个视频"
            } else {
              str+= ""
            }
            return '<a>' + str + '</a>';
          },
          click: function (table, row, lineData) {
            var str = "";
            var count = 0;
            if(lineData.imageList && lineData.imageList.length > 0){
              for (var i = 0; i <  lineData.imageList.length; i++) {
                count++;
                str +=
                  '<img style="" src="' +
                  lineData.imageList[i] +
                  '!max800" width="300" height="300">' +
                  "第" +
                  count +
                  "张";
              }
            }
            if (lineData.videoUrl) {
              str = str + '<br/><video width="300" src="'+lineData.videoUrl+'" controls="controls"><source type="video/mp4" src="'+lineData.videoUrl+'" /></video>'
            }
            Widgets.dialog.html("共" + lineData.imageCount + "张图片", str, {
              width: 800,
            });
          },
        },
        {
          header: '提交时间',
          dataIndex: 'createTime',
          render: function (data) {
            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
          }
        }
      ]
    }, ['dianping!examine-appeal/data/list'], panel, function (dom, obj, item, table) {}).render();
  }


  return {
    list: list
  }

});
