﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'dianping://api/admin/dianping-place/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/dianping-place/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'dianping://api/admin/dianping-place/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/dianping-place/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'dianping://api/admin/dianping-place/view.htm',
            type: 'get'
        }
    }
    
    var listAll = {
        primary: 'id',
        load: {
            url: 'dianping://api/admin/dianping-place/list-all.htm',
            type: 'get',
            format: function(data){
                return data.itemList;
            }
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        listAll:listAll
    }

});