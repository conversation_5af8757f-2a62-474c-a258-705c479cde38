<script id="dianping-template-user-main" type="text/html">
    <?
        var infoData = userData.data;
        var commentData = commentData.data;
    ?>
    
    <div class="col-sm-4">

        <div class="form-horizontal">

            <div class="form-group">
                <label class="col-sm-3 control-label">ID</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" value="<?=infoData.id?>" name="id" readonly/>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">木仓ID</label>
                <div class="col-sm-9">
                    <input type="text" class="form-control" value="<?=infoData.mucangId?>" name="mucangId" readonly/>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">头像</label>
                <div class="col-sm-6">
                    <img src="<?=infoData.avatar?>" style="max-width: 50px;max-height: 50px;border-radius: 50%;" alt=""/>
                </div>
            </div>
            
            <!--昵称-->
            <div class="form-group">
                <label class="col-sm-3 control-label">昵称</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" value="<?=infoData.nickname?>" name="nickname" readonly/>
                </div>
            </div>


            <!--gender-->
            <div class="form-group">
                <label class="col-sm-3 control-label">性别</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" value="<?=(infoData.gender === 'Male' ? '男' : '女')?>" name="gender" readonly/>
                </div>
            </div>



            <? if (infoData.forbidden) {?>
            <div class="form-group">
                <label class="col-sm-3 control-label">禁言信息</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" value="<?=(infoData.forbiddenDay === -1 ? '永久禁言' : '禁言' + infoData.forbiddenDay + '天')?>" name="blacklistForever" readonly/>
                </div>
            </div>
            <?}?>


            <!--blacklistExpiredTime 只有blacklist=true并且infoData.blacklistForever=false的人才显示-->
            <? if (infoData.blacklist && infoData.blacklistForever==false) {?>
            <div class="form-group">
                <label class="col-sm-3 control-label">黑名单期限</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" value="<?=infoData.blacklistExpiredTime?>" name="blacklistExpiredTime" readonly/>
                </div>
            </div>
            <?}?>


            <!--拉黑/解禁 的按钮-->
            <div class="form-group">
                <label class="col-sm-3 control-label"></label>
                <div class="col-sm-3" style="display: flex;">
                    <? if(infoData.blacklist) {?>
                        <a class="btn  btn-info" data-item="releaseBtn">解禁</a>
                    <?} else {?>
                        <a class="btn  btn-warning" data-item="forbidBtn">禁言</a>
                    <?}?>
                    <a class="btn  btn-success" data-item="concatUser" style="margin-left: 20px;">联系用户</a>
                </div>
            </div>
            
          


        </div>


        
       
    </div>
    
    <div class="col-md-8 commentTable" data-item="commentTable" ></div>
</script>