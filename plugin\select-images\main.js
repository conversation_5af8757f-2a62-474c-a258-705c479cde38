﻿/*
 * select-images v0.0.1
 *
 * name: GG
 * date: 2013/12/25
 */

"use strict";

define(['simple!core/template', 'simple!core/ajax'], function (Template, Ajax) {

    var images = function (plugin, success) {

        this.plugin = plugin;
        this.success = success;
        this.config = plugin.config;
        this.images = [];
        if (plugin.config.value) {
            if (plugin.config.parse) {
                this.images = plugin.config.parse(plugin.config.value);
            } else {
                this.images = JSON.parse(plugin.config.value);
            }
        }
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    images.prototype = {

        render: function () {
            var me = this;
            Template(me.plugin.path + '/template/main/btn', me.target, function (dom, data, item, obj) {
                var imageList = item('plugin-image-list');
                var imageInput = item('plugin-image-input');

                imageList.sortable({
                    stop: function (event, item) {
                        me.handleInput(imageList, imageInput);
                    }
                });

                dom.find('input:file').on('change', function () {
                    if (!me.config.multiple && this.files.length > 0) {
                        imageList.empty();
                    }

                    for (var i = 0; i < this.files.length; i++) {
                        var imageText = '';
                        if (me.config.imageText) {
                            imageText = '<input type="text" placeholder="' + me.config.imageText.placeholder + '"/>';
                        }
                        var li = $('<li data-success="false"><p><img /></p><span></span>' + imageText + '<i type="button" class="close">&times;</i></li>').appendTo(imageList);
                        me.uploadImages(this.files[i], function (data, li) {
                            li.find('span').hide();
                            li.attr('data-success', 'true');
                            li.find('img').attr('src', data[0].url);
                            if (data[0].success) {
                                li.parent().next().addClass('hidden')
                            }else{
                                li.parent().next().removeClass('hidden').text(data[0].message+',请重新上传!');
                            }
                            me.handleInput(imageList, imageInput);
                        }, function (data, li) {
                            li.find('span').html('上传失败');
                        }, function (n, li) {
                            li.find('span').html(n + '%');
                        }, li);
                    }
                });

                imageList.delegate('input', 'change', function () {
                    me.handleInput(imageList, imageInput);
                });

                imageList.delegate('.close', 'click', function () {
                    $(this).parent().remove();
                    me.handleInput(imageList, imageInput);
                });

                me.handleInput(imageList, imageInput);
            }, {data: me.images, config: me.config }).render();
        },

        uploadImages: function (file, success, error, progress, index) {
            var me = this;
            var formdata = new FormData();
            formdata.append('bucket', me.config.bucket);
            formdata.append('type', 'image');
            formdata.append('files', file);
            Ajax.request(me.config.uploadUrl || 'simple-upload://upload.htm', {
                data: formdata,
                type: 'post',
                contentType: false,
                processData: false,
                success: function (data) {
                    success.call(this, data, index);
                },
                error: function (data) {
                    error.call(this, data, index);
                },
                progress: function (percentage) {
                    progress.call(this, percentage, index);
                }
            });
        },

        handleInput: function (imageList, imageInput) {
            var me = this;
            var value = [];
            imageList.find('li[data-success=true]').each(function () {
                var o = {};
                o[me.config.imageSrc.dataIndex] = $(this).find('img').attr('src');
                if (me.config.imageText) {
                    o[me.config.imageText.dataIndex] = $(this).find('input').val().trim();
                }
                value.push(o);
            });

            if (me.config.stringify) {
                imageInput.val(me.config.stringify(value));
            } else {
                imageInput.val(JSON.stringify(value));
            }

        }
    }

    images.prototype.constructor = images;

    return function (plugin, success) {
        return new images(plugin, success);
    }

});