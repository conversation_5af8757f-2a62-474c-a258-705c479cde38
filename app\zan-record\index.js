/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'dianping!zan-record/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '点评id：',
                    dataIndex: 'dianpingId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '点评id'
                },
                {
                    header: '用户id：',
                    dataIndex: 'mucangId',
                    xtype: 'text',
                    maxlength: 64,
                    check: 'required',
                    placeholder: '用户id'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '点赞记录列表',
            title: '点赞记录列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'dianping!zan-record/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '点评id：',
                            dataIndex: 'dianpingId'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'mucangId'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'dianping!zan-record/data/view',
                        save: 'dianping!zan-record/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '点评id：',
                            dataIndex: 'dianpingId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '点评id'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'mucangId',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '用户id'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'dianping!zan-record/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '点评id',
                    dataIndex: 'dianpingId'
                },
                {
                    header: '用户id',
                    dataIndex: 'mucangId'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                }
            ]
        }, ['dianping!zan-record/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});