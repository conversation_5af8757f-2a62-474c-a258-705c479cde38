<style>
    .myfancy-outer{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        z-index: 2000;
        background:url(resources/images/overlay.png);
    }
    .myfancy-box{
        position: absolute;
        top:0;
        left: 50%;
        -webkit-transform:translate(-50%,0%);
        background:url(resources/images/overlay.png);
        padding: 5px;
        z-index: 2000;
        margin-top: 50px;
        width: 80%;
        height: 90%;
    }
    .myfancy-nav{
        position: absolute;
        top:0;
        width: 50%;
        height: 100%;
        cursor: pointer;
        text-decoration: none;
        z-index: 200;
    }
    .myfancy-pre{
        left: 0;
    }
    .myfancy-pre span{
        position: absolute;
        top: 50%;
        width: 36px;
        height: 34px;
        margin-top: -18px;
        cursor: pointer;
        z-index: 2000;
        left: 10px;
        background-image: url(resources/images/box_sprite.png);
        background-position: 0 -36px;
        background-repeat: no-repeat;
        visibility: hidden;
    }
    .myfancy-next{
        right: 0
    }
    .myfancy-next span{
        position: absolute;
        top: 50%;
        width: 36px;
        height: 34px;
        margin-top: -18px;
        cursor: pointer;
        z-index: 2000;
        right: 10px;
        background-image: url(resources/images/box_sprite.png);
        background-position: 0 -72px;
        background-repeat: no-repeat;
        visibility: hidden;
    }
    .myfancy-close{
        position: absolute;
        top: -10px;
        right: -18px;
        width: 36px;
        height: 36px;
        cursor: pointer;
        z-index: 2001;
        background-image: url(resources/images/box_sprite.png);
        background-position: 0 0;
        background-repeat: no-repeat;
    }
    .image-desc{
        position: absolute;
        bottom: 0;
        left: 0;
        height:auto;
        width: 100%;
        /*z-index: -100;*/
        background-color:rgba(0, 0, 0, 0.75);
        color:white;
        overflow: hidden;
        word-break:break-all;
    }
    .image-wraper{
        height: 100%;
        width:100%;
        display: -webkit-box;
        -webkit-box-pack:center;
        -webkit-box-align:center;
        overflow: auto;
    }
    .image-rotate{
        position: absolute;
        top:10px;
        right: 45%;
        z-index: 10000;
    }
    .image-rotate button{
        width: 48px;
        height: 48px;
        border:none;
    }
    .image-rotate button:nth-of-type(1){
        width:40px; height: 40px;
        background: url(resources/images/save.png) no-repeat; display: none;
        background-size:40px 40px;
    }
    .image-rotate button:nth-of-type(2){
        background: url(resources/images/rotateL.png) no-repeat;
    }
    .image-rotate button:nth-of-type(3){
        background: url(resources/images/rotateR.png) no-repeat;
    }

</style>
<script id="jiakao-plugin-view-image-template-main-myfancybox" type="text/html">
    <div class="myfancy-outer" data-item="myfancy-outer">
        <div class="myfancy-box" data-item="myfancy-box">
            <a class="myfancy-nav myfancy-pre" data-item="myfancy-pre"><span></span></a>
            <a class="myfancy-nav myfancy-next" data-item="myfancy-next"><span></span></a>
            <a title="Close" class="myfancy-close" data-item="myfancy-close"></a>
            <div class="image-rotate" data-item="image-rotate">
                <button data-item="rotate-save"></button>
                <button data-item="rotate-left"></button>
                <button data-item="rotate-right"></button>
            </div>
            <div data-item="image-wraper" class="image-wraper">
                <div class="image-desc" data-item="image-desc">
                </div>
            </div>

        </div>
    </div>
</script>