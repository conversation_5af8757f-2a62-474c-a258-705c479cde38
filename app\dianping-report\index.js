/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'dianping!dianping-report/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
             {
                 header: '点评编号：',
                 dataIndex: 'dianpingId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '点评编号'
             },
             {
                 header: '举报用户：',
                 dataIndex: 'mucangId',
                 xtype: 'text',
                 maxlength: 64,
                 check: 'required',
                 placeholder: '举报用户'
             },
             {
                 header: '举报时间：',
                 dataIndex: 'reportTime',
                 xtype: 'date',
                 check: 'required',
                 placeholder: '举报时间'
             },
             {
                 header: '举报内容：',
                 dataIndex: 'reportContent',
                 xtype: 'textarea',
                 maxlength: 1024,
                 placeholder: '举报内容'
             },
             {
                 header: '举报次数：',
                 dataIndex: 'reportCount',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '举报次数'
             },
             {
                 header: '处理状态：',
                 dataIndex: 'status',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '处理状态'
             },
             {
                 header: '处理人编号：',
                 dataIndex: 'processUserId',
                 xtype: 'text',
                 placeholder: '处理人编号'
             },
             {
                 header: '处理人姓名：',
                 dataIndex: 'processUsername',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: '处理人姓名'
             },
             {
                 header: '处理时间：',
                 dataIndex: 'processTime',
                 xtype: 'date',
                 placeholder: '处理时间'
             }

            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '举报记录列表',
            title: '举报记录列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                   /* {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }*/
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'dianping!dianping-report/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '点评编号：',
                         dataIndex: 'dianpingId'
                     },
                     {
                         header: '举报用户：',
                         dataIndex: 'mucangId'
                     },
                     {
                         header: '举报时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'reportTime'
                     },
                     {
                         header: '举报内容：',
                         dataIndex: 'reportContent'
                     },
                     {
                         header: '举报次数：',
                         dataIndex: 'reportCount'
                     },
                     {
                         header: '处理状态：',
                         dataIndex: 'status'
                     },
                     {
                         header: '处理人编号：',
                         dataIndex: 'processUserId'
                     },
                     {
                         header: '处理人姓名：',
                         dataIndex: 'processUsername'
                     },
                     {
                         header: '处理时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'processTime'
                     }

                    ]
                },
               /* {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'dianping!dianping-report/data/view',
                        save: 'dianping!dianping-report/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '点评编号：',
                 dataIndex: 'dianpingId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '点评编号'
             },
             {
                 header: '举报用户：',
                 dataIndex: 'mucangId',
                 xtype: 'text',
                 maxlength: 64,
                 check: 'required',
                 placeholder: '举报用户'
             },
             {
                 header: '举报时间：',
                 dataIndex: 'reportTime',
                 xtype: 'date',
                 check: 'required',
                 placeholder: '举报时间'
             },
             {
                 header: '举报内容：',
                 dataIndex: 'reportContent',
                 xtype: 'textarea',
                 maxlength: 1024,
                 placeholder: '举报内容'
             },
             {
                 header: '举报次数：',
                 dataIndex: 'reportCount',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '举报次数'
             },
             {
                 header: '处理状态：',
                 dataIndex: 'status',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '处理状态'
             },
             {
                 header: '处理人编号：',
                 dataIndex: 'processUserId',
                 xtype: 'text',
                 placeholder: '处理人编号'
             },
             {
                 header: '处理人姓名：',
                 dataIndex: 'processUsername',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: '处理人姓名'
             },
             {
                 header: '处理时间：',
                 dataIndex: 'processTime',
                 xtype: 'date',
                 placeholder: '处理时间'
             }

                    ]
                },*/
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'dianping!dianping-report/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '点评编号',
                         dataIndex: 'dianpingId'
                     },
                     {
                         header: '举报用户',
                         dataIndex: 'mucangId'
                     },
                     {
                         header: '举报时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'reportTime'
                     },
                     {
                         header: '举报内容',
                         dataIndex: 'reportContent'
                     },
                     {
                         header: '举报次数',
                         dataIndex: 'reportCount'
                     },
                     {
                         header: '处理状态',
                         dataIndex: 'status'
                     },
                     {
                         header: '处理人编号',
                         dataIndex: 'processUserId'
                     },
                     {
                         header: '处理人姓名',
                         dataIndex: 'processUsername'
                     },
                     {
                         header: '处理时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'processTime'
                     }

            ]
        }, ['dianping!dianping-report/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});