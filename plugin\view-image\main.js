"use strict";

define(['simple!core/template', 'simple!core/store', 'simple!core/widgets'], function (Template, Store, Widgets) {

    var viewImage = function (plugin, success) {
        this.plugin = plugin;
        this.success = success;
        this.config = plugin.config;
        this.index = 0;
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }


    var genThumbImageUrl = function (url) {
        return url ? url.split("!")[0] + '!max800' : ''
    }

    viewImage.prototype = {

        render: function () {
            var me = this;
            var imageData = me.config.data;
            var imgGroup = me.config.container.find(me.config.selector || 'li[data-index]');//.find('[data-item="img-group"]')
            var filter = me.config.filter||false;
            var isStoreImg = me.config.isStoreImg || false;
            var imgLen = imgGroup.length;
            imgGroup.find('*').not('img').on('click', function (e) {
                e.stopPropagation();
            });
            if (!me.config.target) {
                me.config.target = $('<div>');
                $(document.body).append(me.config.target);
            }
            var renderPop = function () {
                me.index = $(this).attr('data-index') || 0;
                var imgSrc;
                var oSrc = $(this).find('img').attr('src')
                if (isStoreImg) {
                    imgSrc = imageData[me.index].url.original
                } else if (filter){
                    if (oSrc.indexOf('auth_key')>-1){
                        imgSrc = oSrc
                    }else {
                        console.log($(this))
                        imgSrc= $(this).find('img').attr('src').replace(/\!.*/ig, '')+'!max800';
                    }
                } else{
                    if (oSrc.indexOf('auth_key')>-1){
                        imgSrc = oSrc
                    }else {
                        imgSrc= $(this).find('img').attr('src').replace(/\!.*/ig, '');
                    }
                }
                var appDom = me.config.target;
                var styleObj = me.getSize(imageData[me.index]);
                Template(me.plugin.path + '/template/main/myfancybox', appDom, function (dom, obj, getItem) {
                    var appImg = $('<img data-processing  data-id="' + imageData[me.index].id + '" class="max-img" style="max-width: 100%; max-height: 100%;height:' + ( styleObj.height ? styleObj.height + 'px' : 'auto') + ';width:' + ( styleObj.width ? styleObj.width + 'px' : 'auto') + ';" src="' + imgSrc + '">');

                    getItem('image-wraper').append(appImg);
                    getItem('image-desc').text(imageData[me.index].description);
                    if (imageData.length > 1) {
                        getItem('myfancy-pre').hover(function () {
                            $(this).find('span').css('visibility', 'visible');
                        }, function () {
                            $(this).find('span').css('visibility', 'hidden');
                        });
                        getItem('myfancy-pre').off('click').on('click', function (e) {
                            e.stopPropagation();
                            if (me.index > 0) {
                                me.index--;
                                appImg.attr('src', '').attr('src', imageData[me.index].url.original);
                                appImg.css('height', me.getSize(imageData[me.index]).height);
                                appImg.css('width', me.getSize(imageData[me.index]).width);
                                getItem('image-desc').text(imageData[me.index].description);
                            } else {
                                me.index = imgLen - 1;
                                appImg.attr('src', '').attr('src', imageData[me.index].url.original);
                                appImg.css('height', me.getSize(imageData[me.index]).height);
                                appImg.css('width', me.getSize(imageData[me.index]).width);
                                getItem('image-desc').text(imageData[me.index].description);
                            }
                        });
                    }


                    getItem('image-desc').off('click').on('click', function (e) {
                        e.stopPropagation();
                    });

                    getItem('image-rotate').find('button').off('click').on('click', function (e) {
                        e.stopPropagation();
                        var dataItem = $(this).attr('data-item');
                        var saveBtn = getItem('rotate-save');
                        var storeUrl;
                        if (me.config.type == 'jiaxiao') {
                            storeUrl = 'jiakao!jiaxiao-image/data/rotateAndSave';
                        } else if (me.config.type == 'coach') {
                            storeUrl = 'jiakao!coach-image/data/rotateAndSave';
                        }
                        if (dataItem == 'rotate-left') {

                            var deg = parseInt(appImg.attr('data-processing-deg'));

                            if (deg == -360 || !deg) {
                                deg = 0;
                            }
                            appImg.trigger('rotate', deg - 90);
                            if ((deg - 90) % 360 == 0) {
                                saveBtn.hide();
                            } else {
                                storeUrl && saveBtn.show();
                            }
                        } else if (dataItem == 'rotate-right') {
                            var deg = parseInt(appImg.attr('data-processing-deg'));
                            if (deg == 360 || !deg) {
                                deg = 0;
                            }
                            appImg.trigger('rotate', deg + 90);
                            if ((deg + 90) % 360 == 0) {
                                saveBtn.hide();
                            } else {
                                storeUrl && saveBtn.show();
                            }
                        } else if (dataItem == 'rotate-save') {
                            if (storeUrl) {
                                var loading = $('<div class="loader" style="position:absolute;top:0;left:0;height:100%;width:100%;"></div>');
                                appImg.css('visibility', 'hidden');
                                appImg.after(loading);
                                appImg.trigger('submit', function (obj) {

                                    Store([storeUrl]).load([
                                        {
                                            load: {
                                                params: {
                                                    id: appImg.attr('data-id'),
                                                    imageData: JSON.stringify(obj[0])
                                                }
                                            }
                                        }
                                    ]).done(function () {
                                        loading.remove();
                                        appImg.css({
                                            '-webkit-transform': 'rotate(0deg)',
                                            'transform': 'rotate(0deg)',
                                            'visibility': 'visible',
                                            'width': me.getSize(obj[0]).width, //obj[0].width,
                                            'height': me.getSize(obj[0]).height //obj[0].height
                                        });
                                        appImg.attr('data-processing-deg', '').attr('src', '').attr('src', obj[0].url);
                                        me.success();
                                    }).fail(function (ret) {
                                        loading.remove();
                                        Widgets.dialog.alert(ret.message);
                                        appImg.css('visibility', 'visible');
                                    });

                                });
                            }
                        }
                    });
                    if (imageData.length > 1) {
                        getItem('myfancy-next').hover(function () {
                            $(this).find('span').css('visibility', 'visible');
                        }, function () {
                            $(this).find('span').css('visibility', 'hidden');
                        });
                        getItem('myfancy-next').off('click').on('click', function (e) {
                            e.stopPropagation();
                            if (me.index < imgLen - 1) {
                                me.index++;
                                appImg.attr('src', '').attr('src', imageData[me.index].url.original);
                                appImg.css('height', me.getSize(imageData[me.index]).height);
                                appImg.css('width', me.getSize(imageData[me.index]).width);
                                getItem('image-desc').text(imageData[me.index].description)
                            } else {
                                me.index = 0;
                                appImg.attr('src', '').attr('src', imageData[me.index].url.original);
                                appImg.css('height', me.getSize(imageData[me.index]).height);
                                appImg.css('width', me.getSize(imageData[me.index]).width);
                                getItem('image-desc').text(imageData[me.index].description)
                            }
                        });
                    }


                    getItem('myfancy-outer').off('click').on('click', function () {
                        appDom.html('');
                        $(document.body).off('.imgGroup');
                    });
                    $(document.body).on('mousewheel.imgGroup', function (event) {
                        // me.mouseWheel(event, me.index, appImg, imgGroup, imgLen, getItem,imageData)
                    });
                    $(document.body).off('keydown.imgGroup').on('keydown.imgGroup', function (e) {
                        if (e.keyCode == 39) {
                            if (me.index < imgLen - 1) {
                                me.index++;
                                appImg.attr('src', '').attr('src', imageData[me.index].url.original);
                                appImg.css('height', me.getSize(imageData[me.index]).height);
                                appImg.css('width', me.getSize(imageData[me.index]).width);
                            } else {
                                me.index = 0;
                                appImg.attr('src', '').attr('src', imageData[me.index].url.original);
                                appImg.css('height', me.getSize(imageData[me.index]).height);
                                appImg.css('width', me.getSize(imageData[me.index]).width);
                            }
                        } else if (e.keyCode == 37) {
                            if (me.index > 0) {
                                me.index--;
                                appImg.attr('src', '').attr('src', imageData[me.index].url.original);
                                appImg.css('height', me.getSize(imageData[me.index]).height);
                                appImg.css('width', me.getSize(imageData[me.index]).width);
                            } else {
                                me.index = imgLen - 1;
                                appImg.attr('src', '').attr('src', imageData[me.index].url.original);
                                appImg.css('height', me.getSize(imageData[me.index]).height);
                                appImg.css('width', me.getSize(imageData[me.index]).width);
                            }
                        }
                    });

                    getItem('myfancy-close').off('click').on('click', function (e) {
                        e.stopPropagation();
                        appDom.html('');
                        $(document.body).off('.imgGroup');
                    });

                    getItem('myfancy-box').bind('contextmenu', function (event) {
                        return false;
                    });


//                        getItem('myfancy-box').on('mousedown', function(e){
//                            e.preventDefault();
//                            var aLink =  $(this).find('a');
//
//                            if(3 == e.which && (maxHeight < imageData[me.index].height || maxWidth < imageData[me.index].width)){
//
//                                if(appImg.hasClass('max-img')){
//
//                                    appImg.css('height',imageData[me.index].height).css('width',imageData[me.index].width);
//                                    aLink.hide();
//                                    appImg.removeClass('max-img');
//                                    $(document.body).off('mousewheel.imgGroup');
//                                }else{
//                                    appImg.css('height', me.getSize(imageData[me.index]).height);
//                                    appImg.css('width', me.getSize(imageData[me.index]).width);
//                                    aLink.show();
//                                    appImg.addClass('max-img');
//                                    $(document.body).on('mousewheel.imgGroup',function(event){
//                                        // me.mouseWheel(event, me.index, appImg, imgGroup, imgLen, getItem, imageData)
//                                    });
//                                }
//                            }
//                        });

                    appImg.off('click').on('click', function (e) {
                        e.preventDefault();
                        e.stopPropagation();
                    })
                }, {}).render();
            }
            imgGroup.off('click').on('click', function () {
                renderPop.call(this);
                return false;
            });

            if (me.config.trigger) {
                renderPop.call(imgGroup[me.config.index || 0]);
            }
        },

        getSize: function (data) {
            var maxHeight = $(window).height() * 0.8;
            var maxWidth = $(window).width() * 0.9;
            var styleObj = {};
            if (data.height > data.width) {
                if (data.height <= maxHeight) {
                    styleObj.height = data.height;
                    styleObj.width = data.width;
                } else {
                    styleObj.height = maxHeight;
                    styleObj.width = maxHeight / data.height * data.width;
                }
            } else {
                if (data.width <= maxWidth) {
                    styleObj.height = data.height;
                    styleObj.width = data.width;
                } else {
                    styleObj.width = maxWidth;
                    styleObj.height = maxWidth / data.width * data.height;
                }
            }
            if (!styleObj.height) styleObj.height = '';
            if (!styleObj.width) styleObj.width = '';
            return styleObj;
        },

        mouseWheel: function (event, indexNum, appImg, imgGroup, imgLen, getItem, imageData) {
            var me = this;
            me.index = indexNum;
            var deltaX = event.originalEvent.deltaX;
            var deltaY = event.originalEvent.deltaY;
            if (deltaY > 0) {
                if (me.index < imgLen - 1) {
                    me.index++;
                    var currentImg = imgGroup.eq(me.index);
                    appImg.attr('src', imageData[me.index].url.original);
                    getItem('image-desc').text(imageData[me.index].description);
                } else {
                    me.index = 0;
                    var currentImg = imgGroup.eq(me.index);
                    appImg.attr('src', imageData[me.index].url.original);
                    getItem('image-desc').text(imageData[me.index].description);
                }
            } else if (deltaY < 0) {
                if (me.index > 0) {
                    me.index--;
                    var currentImg = imgGroup.eq(me.index);
                    appImg.attr('src', imageData[me.index].url.original);
                    getItem('image-desc').text(imageData[me.index].description);
                } else {
                    me.index = imgLen - 1;
                    var currentImg = imgGroup.eq(me.index);
                    appImg.attr('src', imageData[me.index].url.original);
                    getItem('image-desc').text(imageData[me.index].description);
                }
            }
        }
    }

    viewImage.prototype.constructor = viewImage;

    return function (plugin, success) {
        return new viewImage(plugin, success);
    }
});
