/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */
"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {
  const reasonMap = [
    {
      key: "",
      value: "请选择进审原因"
    },
    {
      key: "先审后发",
      value: "先审后发"
    },
    {
      key: "策略",
      value: "策略"
    },
  ]
  var getSelectedIds = function (dom) {
    //检测哪些checkbox被打勾了 记录ids
    var idsArr = [];
    var myIds = '';
    dom.find("input[class='oCheck']:checkbox").each(function () {
      if ($(this)[0].checked == true) {
        myIds = $.trim($(this).parent().parent().find("td[title='#']").text()); // string本行的#(id)
        idsArr.push(parseInt(myIds));
      }
    });
    return idsArr.join(',');
  }

  var auditPass = function (table, paramStr) {
    if (paramStr.length <= 0) return Simple.Dialog.alert('先勾选啊')
    // audit(table, 'true');
    isAudit(table, paramStr, 'pass')
  }

  var auditFail = function (table, paramStr) {
    if (paramStr.length <= 0) return Simple.Dialog.alert('先勾选啊')
    // audit(table, 'false');
    isAudit(table, paramStr, 'fail')
  }
  var getTableList = []
  let isAudit = function (Tables, paramStr, type) {
    let hasRisk = []
    let hasSelectArray = []
    hasRisk = getTableList.filter((ele) => {
      paramStr.forEach((arr) => {
        if (arr == ele.id) {
          hasSelectArray.push(ele.id)
        }
      })
      let tag1 = (ele.reason.length == 4 && ele.reason == "先审后发") || ele.reason.length == 0
      let tag2 = type == "pass" ? !tag1 : tag1
      return paramStr.indexOf(ele.id + '') !== -1 && tag2
    })
    var reductArray = []
    Widgets.dialog.html(type == 'pass' ? '请确认 - 审核通过' : '请确认 - 审核失败', {
      height: 600,
      width: 900,
      buttons: [
        {
          name: type == 'pass' ? '审核通过' : '审核失败',
          xtype: type == 'pass' ? 'primary' : 'warning',
          id: 'submit-tips',
          class: 'submit-tips',
          click: function (obj) {

            var getSelectRadio = document.getElementsByClassName("monidianjuimonidianjui")
            $(getSelectRadio).click()
            let noChecked = hasRisk.filter((ele) => {
              return reductArray.indexOf(ele.id + "") == -1
            })
            let noCheckedId = []
            noChecked.forEach((ele) => {
              noCheckedId.push(ele.id)
            })

            let newArray = hasSelectArray.filter((ele) => {
              return noCheckedId.indexOf(ele) == -1
            })
            let ids = newArray.join(",")
            let tag = type == "pass" ? true : false
            this.close();
            Simple.Store2('dianping!dianping-reply-review/data/audit', {
              idList: ids,
              pass: tag
            }, 'save').then(function () {
              Simple.Dialog.toast('批量审核成功!')
              Tables.render()
            }, function () {
              Simple.Dialog.alert(arguments[1].message || 'error')
            })
          }
        },
        {
          name: '取消',
          xtype: 'warning',
          click: function () {
            this.close();
          }
        }
      ]
    }).done(function (dialog) {
      setTimeout(() => {
        let parent = dialog.body
        var string1 = type == "pass" ? '提示风险' : '建议可通过'
        var string2 = type == "pass" ? '审核通过' : '审核失败'
        var string3 = "请将需要【" + string2 + "】的内容勾选"
        dialog.body.prepend("<div style='position:absolute;top:0px;width:96%;background:#fff;height:60px;margin-bottom:30px'><div  style='color:rgb(163,0,142);padding-right:30px;font-size:18px;margin-top:10px'>已勾选" + hasSelectArray.length + "</span> 条, 其中<span id='tips-title2'>" + hasRisk.length + "</span>条机器" + string1 + ", 请再次核对↓↓↓↓↓ </div><div  style='color:rgb(163,0,142);padding-right:30px;font-size:18px;margin-top:0px'>" + string3 + "</div></div>")
        let lastParent = parent[0].parentNode.parentNode.parentNode
        let childrens = $(lastParent).children()
        let footer = childrens[2]
        let footerdiv = $(footer).children()
        $(footerdiv[0]).prepend("<span  style='color:rgb(163,0,142);padding-right:30px;font-size:15px;margin-top:30px'>上方列表里勾选的内容会被“" + string2 + "”请核对后再点击→</span>")
      }, 0)
      Table({
        width: 800,
        buttons: {
          top: [
            {
              name: '',
              class: 'primary monidianjuimonidianjui',
              click: function (table, dom, config, arr) {
                reductArray = arr
              }
            },
          ]
        },
        renderAfter: function (table, dom) {

        },

        selector: {
          dataIndex: 'id',
        },
        columns: [
          {
            header: '#',
            dataIndex: 'id',
            width: 80
          },
          {
            header: '原因',
            dataIndex: 'reason',
            width: 80,
            render: function (data) {
              return "<div style='white-space: pre-line;'>" + data + "</div>"
            }
          },
          {
            header: '内容',
            dataIndex: 'content',
            width: 500,
            render: function (data) {
              return '<div style="max-width: 450px;">' + data + '</div>'
            }
          },
          {
            header: '图片',
            dataIndex: 'imageCount',
            render: function (data, arrData, lineData, index) {
              return lineData.imageCount > 0 ? '<a>' + lineData.imageCount + '图</a>' : ''
            },
            click: function (table, row, lineData) {
              var str = '';
              var count = 0;
              for (var i = 0; i < lineData.imageList.length; i++) {
                count++;
                str += '<img style="" src="' + lineData.imageList[i].url + '!max800" width="300" height="300">' + '第' + count + '张';
              }
              Widgets.dialog.html(+ lineData.imageCount + '图', str, {
                width: 800
              })
            }
          },
        ]
      }, { data: hasRisk }, $(dialog.body), function (target, table, item) {
        let tag = target.item('description')
        if (tag[0]) {
          tag[0].style.color = "rgb(163,0,142)"
          tag[0].style.fontSize = "18px"
        }
        let checkbox = item('table-selector')
        let checkboxHeader = item('table-header-selector')
        $(checkbox).css({ width: "28px", height: "26px" })
        $(checkboxHeader).css({ width: "28px", height: "26px" })
        $(target[0]).css("marginTop", "60px")
        if (hasRisk.length > 0) {
          $(checkbox).attr("checked", true)
          $(checkboxHeader).attr("checked", true)
        }

      }).render();


    })
  }
  var audit = function (table, pass) {
    var paramStr = getSelectedIds(table.dom)

    if (!paramStr) return Simple.Dialog.alert('先勾选啊')
    isAudit(Tables, paramStr, 'pass')
    // Simple.Store2('dianping!dianping-reply-review/data/audit', {
    //   idList: paramStr,
    //   pass: pass
    // }, 'save').then(function () {
    //   Simple.Dialog.toast('批量审核成功!')
    //   table.render()
    // }, function () {
    //   Simple.Dialog.alert(arguments[1].message || 'error')
    // })
  }

  var list = function (panel) {
    Table({
      description: '待审核回复列表',
      title: '待审核回复列表',
      search: [
        {
          xtype: 'select',
          dataIndex: 'placeToken',
          index: {
            key: 'token',
            value: 'name'
          },
          store: 'dianping!dianping-place/data/listAll',
          insert: [
            {
              name: '请选择点评位',
              token: ''
            }
          ]
        },
        {
          xtype: "select",
          dataIndex: "reason",
          store: reasonMap,
          placeholder: "请选择进审原因",
        }
      ],
      buttons: {
        top: [
          {
            name: '刷新',
            class: 'info',
            click: function (obj) {
              obj.render();
            }
          },
          {
            name: '审核通过',
            class: 'primary',
            click: function (table, dom, config, arr) {
              auditPass(table, arr)
            }
          },
          {
            name: '审核失败',
            class: 'danger',
            click: function (table, dom, config, arr) {
              auditFail(table, arr)
            }
          }
        ],
        bottom: [
          {
            name: '刷新',
            class: 'info',
            click: function (obj) {
              obj.render();
            }
          }
        ]
      },
      operations: [
        //行点击事件
        //   {
        //     name: '',
        //     lineSelector: true,
        //     click: function (table, row, lineData) {
        //         let domE = row[0]
        //         let children = $(domE).children()
        //         let checkBox = children[0]
        //         let input = $(checkBox).find("input")[0]
        //         let $inputDom = $(input)
        //         let checked = $inputDom.prop("checked")
        //         if (checked) {
        //             $inputDom.removeAttr("checked")
        //         } else {
        //             $inputDom.prop("checked", true)

        //         }

        //     }
        // },
        {
          name: '审核通过',
          class: 'success',
          click: function (table, lineDom, lineData) {
            let tag1 = lineData.reason.length == 4 && lineData.reason == "先审后发"
            let string = ""

            if (!tag1) {
              string = "此内容包含风险"
            }

            Widgets.dialog.confirm('确定审核通过 #' + lineData.id + "," + string + "？", function (e, state) {
              if (state) {

                Simple.Store2('dianping!dianping-reply-review/data/audit', {
                  idList: lineData.id,
                  pass: 'true'
                }, 'save').then(function () {
                  Simple.Dialog.alert('已通过!')
                  table.render()
                }, function () {
                  Simple.Dialog.alert(arguments[1].message || 'error')
                })
              }
            });

          }
        },
        {
          name: '审核失败',
          class: 'danger',
          click: function (table, lineDom, lineData) {
            let tag1 = lineData.reason.length == 4 && lineData.reason == "先审后发"
            let string = ""
            if (tag1) {
              string = "此内容包含无风险"
            }
            Widgets.dialog.confirm('确定审核失败 #' + lineData.id + "," + string + "？", function (e, state) {
              if (state) {
                Simple.Store2('dianping!dianping-reply-review/data/audit', {
                  idList: lineData.id,
                  pass: 'false'
                }, 'save').then(function () {
                  Simple.Dialog.alert('已删除！')
                  table.render()
                }, function () {
                  Simple.Dialog.alert(arguments[1].message || 'error')
                })
              }
            });

          }
        },
        {
          name: '查看用户',
          class: 'info',
          click: function (table, row, lineData) {
            Simple.Layout.toPanel('user-' + lineData.id, lineData.nickname, 'dianping!app/user-detail/index/edit', {
              id: lineData.id,
              authorId: lineData.authorId
            })
          }
        }
      ],
      selector: {
        dataIndex: 'id',
      },
      columns: [
        // {
        //   header: function () {
        //     return '<input class="allCheck" type="checkbox">';
        //   },
        //   render: function () {
        //     return '<input class="oCheck" type="checkbox">';
        //   }
        // },
        {
          header: '#',
          dataIndex: 'id'
        },
        {
          header: '用户',
          dataIndex: 'avatar',
          render: function (data, table, row) {
            return '<div style="cursor: pointer;">'
              + '<img class="userAvatar" src="' + data + '" style="max-width:50px; max-height:50px;border-radius: 25px;">' + '<br/>'
              + '<span class="userNickname" >' + row.nickname + '</span>'
              + '</div>';
          }
        },
        {
          header: '点评编号',
          dataIndex: 'dianpingId'
        },
        {
          header: '点评位：',
          dataIndex: 'placeName'
        },
        {
          header: "学员评分",
          dataIndex: "score",
          width: 90,
        },
        {
          header: '内容',
          dataIndex: 'content'
        },
        {
          header: '原因',
          dataIndex: 'reason',
          render: function (data) {
            return "<div style='white-space: pre-line;'>" + data + "</div>"
          }
        },
        {
          header: '状态',
          dataIndex: 'status',
          render: function (data) {
            var map = {
              0: '正常',
              1: '待审核',
              2: '已删除',
              3: '已忽略'
            }
            return map[data]
          }
        },
        {
          header: '创建时间',
          dataIndex: 'createTime',
          render: function (data) {
            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
          }
        }
      ]
    }, ['dianping!dianping-reply-review/data/list'], panel, function (dom, obj, item, table) {
      let list = obj.data
      getTableList = list
      let checkbox = item('table-selector')
      let checkboxHeader = item('table-header-selector')
      $(checkbox).css({ width: "28px", height: "26px" })
      $(checkboxHeader).css({ width: "28px", height: "26px" })
      dom.find('input.oCheck').parent().on('click', function (e) {
        var $oCheck = $(e.target).find('input')
        $oCheck.click()
      })

      dom.find('.allCheck').on('click', function () {
        dom.find('.oCheck').click();
      });

      dom.find('.userAvatar').on('click', function () {
        $(this).parent().parent().find("[title=查看用户]").click();
      });

      $('.userNickname').on('click', function () {
        $(this).parent().parent().find("[title=查看用户]").click();
      });

    }).render();
  }

  return {
    list: list
  }

});