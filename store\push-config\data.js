﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON>a
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'dianping://api/admin/push-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/push-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'dianping://api/admin/push-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'dianping://api/admin/push-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'dianping://api/admin/push-config/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view
    }

});