'use strict';

define(['simple!core/template', 'simple!core/widgets', 'simple!core/plugin'], function (Template, Widgets, Plugin) {
    var ImageGallery = function (plugin, success) {
        this.plugin = plugin;
        this.config = plugin.config;
        this.target = $('[data-plugin=' + plugin.id + ']');

        this.render();
        success(this);
    };

    ImageGallery.prototype = {
        render: function () {
            var me = this;
            var imageList = me.config.imageList || [];
            var dianpingId = me.config.dianpingId;

            Template(me.plugin.path + '/template/main/index', null, null, {
                imageList: imageList,
                dianpingId: dianpingId
            }).render().done(function (obj, dom) {
                Widgets.dialog.html("共" + imageList.length + "张图片", dom, {
                    width: 800
                });

                // 使用 jQuery 的 on 方法绑定事件
                $(dom).on('click', '.edit-image-btn', function () {
                    console.log(this)
                    var imageId = $(this).data('image-id');
                    me.editImage(imageId, dianpingId);
                });
            });
        },

        editImage: function (imageId, dianpingId) {
            Plugin('dianping!image-mosaic', {
                image: {
                    url: imageId
                },
                onSave: function(imageData) {
                    Widgets.dialog.alert('图片编辑成功', function() {
                        // 刷新当前页面或重新加载图片
                        window.location.reload();
                    });
                }
            }).render();
        }
    };

    ImageGallery.prototype.constructor = ImageGallery;

    return function (plugin, success) {
        return new ImageGallery(plugin, success);
    };
});
