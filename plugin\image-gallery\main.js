'use strict';

define(['simple!core/template', 'simple!core/widgets', 'simple!core/plugin'], function (Template, Widgets, Plugin) {
    var ImageGallery = function (plugin, success) {
        this.plugin = plugin;
        this.config = plugin.config;
        this.target = $('[data-plugin=' + plugin.id + ']');

        this.render();
        success(this);
    };

    ImageGallery.prototype = {
        render: function () {
            var me = this;
            var imageList = me.config.imageList || [];
            var dianpingId = me.config.dianpingId;

            Template(me.plugin.path + '/template/main/index', null, null, {
                imageList: imageList,
                dianpingId: dianpingId
            }).render().done(function (obj, dom) {
                Widgets.dialog.html("共" + imageList.length + "张图片", dom, {
                    width: 800
                }).done(function (dialog) {
                    dialog.body.find('.edit-image-btn').on('click', function () {
                        var imageUrl = $(this).data('image-url');
                        me.editImage(imageUrl, dianpingId);
                    })
                })
            });
        },

        editImage: function (imageUrl, dianpingId) {
            console.log('开始编辑图片，imageUrl:', imageUrl, 'dianpingId:', dianpingId);

            Plugin('dianping!image-mosaic', {
                image: {
                    url: imageUrl + '!max800'
                },
                onSave: function(imageData) {
                    console.log('图片编辑完成，保存数据:', imageData);
                }
            }).render();
        }
    };

    ImageGallery.prototype.constructor = ImageGallery;

    return function (plugin, success) {
        return new ImageGallery(plugin, success);
    };
});
