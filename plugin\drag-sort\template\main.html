﻿﻿<!--
    * main v1.0
    *
    * name: GG
    * date: 2014/02/28
-->

<!-- 列表模板 -->
<script id="cheyouquan-plugin-drag-sort-template-main-list" type="text/html">
    <style>
        .drag-sort-list {
            border: 1px solid #ddd;
            margin: 0 25px;
        }

        .drag-sort-list li {
            margin: 3px;
            padding: 6px 15px;
            height: 30px;
            background: #e6e6e6;
            list-style-type: decimal;
        }

        .drag-sort-list li p {
            white-space: nowrap;
            padding-right: 85px;
            margin: 0;
        }

        .drag-sort-list li p .glyphicon {
            margin-right: 5px;
        }

        .drag-sort-list li a {
            text-decoration: none;
            color: #FFF;
        }

        .drag-sort-list li input {
            width: 40px;
            height: 20px;
            margin-top: -1px;
            margin-right: 5px;
        }

    </style>

    <? var data = list.data ?>
    <ul class="drag-sort-list">
        <? for(var i = 0; i < data.length; i++) { ?>
        <li data-id="<?=data[i].relationId?>">
            <a class="label label-default pull-right" data-item="goto-btn">Goto</a>
            <a class="label label-default pull-right edit hidden" data-item="go-btn">Go!</a>
            <input type="text" class="pull-right edit hidden" />
            <p><span class="glyphicon glyphicon-sort"></span><?=data[i].name ?></p>
        </li>

        <? } ?>
    </ul>
</script>
