/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

'use strict';

define(function () {

  var header = {
    load: {
      type: 'data',
      data: [
        {
          name: '点评',
          item: 'header-nav',
          aside: 'dianping!main/aside'
        },
        {
          name: '报表',
          item: 'header-nav',
          aside: 'dianping!main/aside1'
        },
        {
          name: '防水墙',
          item: 'header-nav',
          aside: 'dianping!main/aside2'
        },
        {
          name: '系统管理',
          item: 'header-nav',
          aside: 'systemadmin!main/aside?project=dianping'
        }
      ]
    }
  }

  var aside = {
    load: {
      type: 'data',
      data: [
        {
          name: '点评系统',
          nodes: [
            {
              id: 'dianping-pending',
              name: '待审核点评',
              app: 'dianping!app/dianping/index/listPending',
              item: 'aside-nav'
            },
            {
              id: 'dianping-reply-review',
              name: '待审核回复',
              app: 'dianping!app/dianping-reply-review/index/list',
              item: 'aside-nav'
            },
            {
              id: 'dianping-follow-up',
              name: '待审核追评',
              app: 'dianping!app/dianping-follow-up/index/list',
              item: 'aside-nav'
            },
            {
              id: 'examine-appeal',
              name: '待审核申诉',
              app: 'dianping!app/examine-appeal/index/list',
              item: 'aside-nav'
            },
            {
                id: 'dianping-pending-complaint',
                name: '待审核投诉',
                app: 'dianping!app/dianping-pending-complaint/index/list',
                item: 'aside-nav'
            },
            {
              id: 'dianping',
              name: '点评列表',
              app: 'dianping!app/dianping/index/list',
              item: 'aside-nav'
            },
            {
              id: 'dianping-reply',
              name: '回复列表',
              app: 'dianping!app/dianping-reply/index/list',
              item: 'aside-nav'
            },
            {
              id: 'appeal-list',
              name: '申诉列表',
              app: 'dianping!app/appeal-list/index/list',
              item: 'aside-nav'
            },
            {
              id: 'dianping-pending-list',
              name: '投诉列表',
              app: 'dianping!app/dianping-pending-complaint/index/complaintList',
              item: 'aside-nav'
            },
          ]
        },
        {
          name: '系统管理',
          nodes: [
            {
              id: 'dianping-place',
              name: '点评位置',
              app: 'dianping!app/dianping-place/index/list',
              item: 'aside-nav'
            },
            {
              id: 'userinfo',
              name: '用户数据',
              app: 'dianping!app/userinfo/index/list',
              item: 'aside-nav'
            },
            {
              id: 'zan-record',
              name: '点赞记录',
              app: 'dianping!app/zan-record/index/list',
              item: 'aside-nav'
            },
            {
              id: 'dianping-image',
              name: '图片数据',
              app: 'dianping!app/dianping-image/index/list',
              item: 'aside-nav'
            },
            {
              id: 'dianping-report',
              name: '举报记录',
              app: 'dianping!app/dianping-report/index/list',
              item: 'aside-nav'
            },
            {
              id: 'manage-log',
              name: '管理日志',
              app: 'dianping!app/manage-log/index/list',
              item: 'aside-nav'
            },
            {
              id: 'admin-mapping',
              name: '管理员映射',
              app: 'dianping!app/admin-mapping/index/list',
              item: 'aside-nav'
            }
          ]
        }
      ]
    }
  }
  var aside1 = {
    load: {
      type: 'data',
      data: [
        {
          name: '报表',
          nodes: [
            {
              id: 'stat-daily-new',
              name: '驾校每日新增',
              app: 'dianping!app/stat/index/list1',
              item: 'aside-nav'
            },
            {
              id: 'stat-daily-coach-new',
              name: '教练每日新增',
              app: 'dianping!app/stat-coach/index/list1',
              item: 'aside-nav'
            },
            {
              id: 'stat-manage-log',
              name: '管理日志',
              app: 'dianping!app/stat/index/list2',
              item: 'aside-nav'
            }
          ]
        }
      ]
    }
  }


  var aside2 = {
    load: {
      type: 'data',
      data: [
        {
          name: '防水墙',
          nodes: [
            {
              id: 'violate-word',
              name: '违禁词',
              app: 'firewall!app/violate-word/index/list',
              item: 'aside-nav',
              libraryId: 4
            },
            {
              id: 'user-forbid',
              name: '禁言用户',
              app: 'firewall!app/user-forbid/index/list',
              item: 'aside-nav',
              libraryId: 4
            }
          ]
        }
      ]
    }
  }

  return {
    header: header,
    aside: aside,
    aside1: aside1,
    aside2: aside2
  }

});
