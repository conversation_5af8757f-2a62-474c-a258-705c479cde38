/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */
"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

  var statusMap = {
    0: '正常',
    1: '待审核',
    2: '已删除'
  }

  var getSelectedIds = function (dom) {
    var idsArr = [];
    var myIds = '';
    dom.find("input[class='oCheck']:checkbox").each(function () {
      if ($(this)[0].checked == true) {
        myIds = $.trim($(this).parent().parent().find("td[title='#']").text()); //string   本行的#(id)
        idsArr.push(parseInt(myIds));
      }
    });
    return idsArr.join(',');
  }

  var del = function (table) {
    var paramStr = getSelectedIds(table.dom)

    if (!paramStr) return Simple.Dialog.alert('先勾选啊')

    Simple.Store2('dianping!dianping-reply/data/delete', {
      idList: paramStr,
    }, 'save').then(function () {
      Simple.Dialog.toast('批量删除成功!')
      table.render()
    }, function () {
      Simple.Dialog.alert(arguments[1].message || 'error')

    })
  }

  var list = function (panel) {
    Table({
      description: '点评回复列表',
      title: '点评回复列表',
      search: [
        {
          xtype: 'text',
          dataIndex: 'id',
          placeholder: '编号'
        },
        {
          xtype: 'select',
          dataIndex: 'status',
          store: Simple.Utils.MapToStoreArray(statusMap, {'': '请选择状态'}),
          value: 0
        },
        {
          xtype: 'select',
          dataIndex: 'placeToken',
          index: {
            key: 'token',
            value: 'name'
          },
          store: 'dianping!dianping-place/data/listAll',
          insert: [
            {
              name: '请选择点评位',
              token: ''
            }
          ]
        },
        {
          xtype: 'text',
          dataIndex: 'dianpingId',
          placeholder: '点评编号'
        },
        {
          xtype: 'text',
          dataIndex: 'content',
          placeholder: '内容'
        },
      ],
      buttons: {
        top: [
          {
            name: '刷新',
            class: 'info',
            click: function (obj) {
              obj.render();
            }
          },
          {
            name: '批量删除',
            class: 'danger',
            click: del
          }
        ],
        bottom: [
          {
            name: '刷新',
            class: 'info',
            click: function (obj) {
              obj.render();
            }
          }
        ]
      },
      operations: [
        {
          name: '查看',
          xtype: 'view',
          width: 400,
          class: 'success',
          title: '查看',
          store: 'dianping!dianping-reply/data/view',
          columns: [
            {
              header: '#',
              dataIndex: 'id'
            },
            {
              header: '作者编号：',
              dataIndex: 'authorId'
            },
            {
              header: '点评编号：',
              dataIndex: 'dianpingId'
            },
            {
              header: '点评位：',
              dataIndex: 'placeName'
            },
            {
              header: '内容：',
              dataIndex: 'content'
            },
            {
              header: '地点：',
              dataIndex: 'location'
            },
            {
              header: '地址：',
              dataIndex: 'address'
            },
            {
              header: '状态：',
              dataIndex: 'status'
            },
            {
              header: '扩展数据：',
              dataIndex: 'extraData'
            },
            {
              header: '创建时间：',
              dataIndex: 'createTime',
              render: function (data) {
                return Simple.render.date(data)
              }
            }
          ]
        },
        {
          name: '删除',
          class: 'danger',
          click: function (table, lineDom, lineData) {
            Simple.Store2('dianping!dianping-reply/data/delete', {
              idList: lineData.id
            }, 'save').then(function () {
              table.render()
            }, function () {
              Simple.Dialog.alert(arguments[1].message || 'error')
            })
          }
        },
        {
          name: '查看用户',
          class: 'info',
          click: function (table, row, lineData) {
            Simple.Layout.toPanel('user-' + lineData.id, lineData.nickname, 'dianping!app/user-detail/index/edit', {
              id: lineData.id,
              authorId: lineData.authorId
            })
          }
        }
      ],
      columns: [
        {
          header: function () {
            return '<input class="allCheck" type="checkbox">';
          },
          render: function () {
            return '<input class="oCheck" type="checkbox">';
          }
        },
        {
          header: '#',
          dataIndex: 'id',
          width: 20
        },
        {
          header: '用户',
          dataIndex: 'avatar',
          width: 150,
          render: function (data, table, row) {
            return '<div style="cursor: pointer;">'
              + '<img class="userAvatar" src="' + data + '" style="max-width:50px; max-height:50px;border-radius: 25px;">' + '<br/>'
              + '<span class="userNickname" >' + row.nickname + '</span>'
              + '</div>';
          }
        },
        {
          header: '点评编号',
          dataIndex: 'dianpingId'
        },
        {
          header: '点评位：',
          dataIndex: 'placeName'
        },
        {
          header: "学员评分",
          dataIndex: "score",
          width: 90,
        },
        {
          header: '内容',
          dataIndex: 'content',
          width: 300
        },
        {
          header: '地点',
          dataIndex: 'location'
        },
        {
          header: '地址',
          dataIndex: 'address'
        },
        {
          header: '状态',
          dataIndex: 'status',
          render: function (data) {
            var map = {
              0: '正常',
              1: '待审核',
              2: '已删除',
              3: '已忽略'
            }
            return map[data]
          }
        },
        {
          header: '创建时间',
          dataIndex: 'createTime',
          render: function (data) {
            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
          }
        }
      ]
    }, ['dianping!dianping-reply/data/list'], panel, function (dom, obj, item, table) {

      dom.find('input.oCheck').parent().on('click', function (e) {
        var $oCheck = $(e.target).find('input')
        $oCheck.click()
      })

      dom.find('.allCheck').on('click', function () {
        dom.find('.oCheck').click();
      });

      dom.find('.userAvatar').on('click', function () {
        $(this).parent().parent().find("[title=查看用户]").click();
      });

      $('.userNickname').on('click', function () {
        $(this).parent().parent().find("[title=查看用户]").click();
      });
    }).render();
  }

  return {
    list: list
  }

});