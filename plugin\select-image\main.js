﻿/*
 * select-image v0.0.1
 *
 * name: GG
 * date: 2014/03/13
 */

"use strict";

define(['simple!core/template', 'simple!core/ajax'], function (Template, Ajax) {

    var image = function (plugin, success) {
        

        this.plugin = plugin;
        this.success = success;
        this.config = plugin.config;
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    };
    var changeSize = false;
    image.prototype = {

        render: function () {
            var me = this;
            Template(me.plugin.path + '/template/main/select', me.target, function (dom, data, item, obj) {

                var image = dom.find('img');
                var process = dom.find('.upload-process');
                var input = dom.find('input[type=hidden]');

                dom.find('input:file').on('change', function () {
                    if (this.files.length > 0) {
                        process.animate({ bottom: '0px' }, 500);
                        me.uploadImage(this.files[0], function (data) {
                            if (changeSize) {
                                image.removeClass('hidden').attr('src', data[0].url + '!245.140');
                            } else {
                                image.removeClass('hidden').attr('src', data[0].url);
                            }
                            if (data[0].success) {
                                image.parent().next().addClass('hidden')
                            } else {
                                image.parent().next().removeClass('hidden').text(data[0].message + ',请重新上传!');
                            }
                            input.val(data[0].url);
                            input.attr('data-width', data[0].width);
                            input.attr('data-height', data[0].height);
                            process.animate({bottom: '-30px'}, 500);
                            //执行回调
                            me.config.success && me.config.success.apply(this, data[0].url);
                        }, function (data) {
                            process.html('上传失败');
                        }, function (n) {
                            process.html(n + '%');
                        });
                    }
                });
            }, {config: me.config }).render();
        },

        uploadImage: function (file, success, error, progress) {
            var me = this;
            var formData = new FormData();
            formData.append('bucket', me.config.bucket);
            formData.append('type', 'image');
            formData.append('files', file);
            Ajax.request(me.config.uploadUrl || 'simple-upload://upload.htm', {
                data: formData,
                type: 'post',
                contentType: false,
                processData: false,
                success: function (data) {
                    if (me.config.done) {
                        me.config.done(data);
                    };
                    changeSize = me.config.flag;
                    success.call(this, data);
                },
                error: function (data) {
                    error.call(this, data);
                },
                progress: function (percentage) {
                    progress.call(this, percentage);
                }
            });
        }
    };

    image.prototype.constructor = image;

    return function (plugin, success) {
        return new image(plugin, success);
    }

});