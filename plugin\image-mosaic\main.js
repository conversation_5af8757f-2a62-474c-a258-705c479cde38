'use strict'

define(['simple!core/template', 'simple!core/widgets'], function (Template, Widgets) {
  var ImageMosaic = function (plugin, success) {
    this.plugin = plugin
    this.config = plugin.config
    this.data = plugin.data
    this.target = $('[data-plugin=' + plugin.id + ']')

    this.state = {
      // 实际宽度（导出的分辨率）
      canvasWidth: 1000,
      // 实际高度（导出的分辨率）
      canvasHeight: 1000,
      // 是否正在绘制马赛克
      isDrawing: false,
      // 马赛克的块大小
      mosaicSize: 30,

      history: []
    }

    this.render()

    success(this)
  }

  ImageMosaic.prototype = {
    render: function () {
      var me = this

      var result = Template(me.plugin.path + '/template/main/index', null, null, {
        data: this.data
      }).render()

      result.done(function (obj, dom, data, item) {

        me.loadImage(function () {
          var width = Math.min(me.currentImage.width + 40, 820)

          Widgets.dialog
            .html('图片编辑', dom, {
              width: width,
              backdrop: 'static',
              buttons: [
                {
                  name: '保存',
                  xtype: 'close',
                  class: 'primary',
                  click: function () {
                    var image = me.exportImage()

                    me.config.onSave && me.config.onSave(image)

                    this.close()
                  }
                }
              ]
            })
            .done(function (dialog) {
              me.initCanvas(dialog.body)

              me.initialize(dialog.body)
            })
        })
      })

      return result
    },

    initialize: function (content) {
      var me = this

      var canvasEL = this.canvasEL

      var setLabelText = function (text) {
        var label = content.find('.mosaic-bar > .bar-label')

        label.text('马赛克尺寸' + text)
      }

      content.find('.mosaic-bar > .bar').on('input', function () {
        var value = $(this).val()

        setLabelText(value)

        me.state.mosaicSize = parseInt(value)
      })

      content.find('.undo-btn').on('click', function () {
        me.undo()
      })

      canvasEL.addEventListener('mousedown', function () {
        me.state.isDrawing = true

        // 在开始绘制之前保存当前状态
        me.saveState()
      })

      canvasEL.addEventListener('mouseup', function () {
        me.state.isDrawing = false
      })

      canvasEL.addEventListener('mousemove', function (event) {
        me.drawMosaic(event)
      })

      setLabelText(30)
    },

    initCanvas: function (content) {
      var canvasEL = content.find('.canvas-container > canvas')[0]

      var canvasCtx = canvasEL.getContext('2d')

      this.canvasEL = canvasEL

      this.canvasCtx = canvasCtx

      this.canvasEL.width = this.state.canvasWidth = this.currentImage.width
      this.canvasEL.height = this.state.canvasHeight = this.currentImage.height

      // 绘制图像，并保持宽高比不变
      this.canvasCtx.drawImage(this.currentImage, 0, 0, this.currentImage.width, this.currentImage.height)

      // 保存初始状态
      this.saveState()
    },

    loadImage: function (callback) {
      var currentImage = new Image()

      currentImage.crossOrigin = 'anonymous'

      currentImage.src = this.config.image[this.config.urlKey || 'url'] // 替换为你的图片 URL

      this.currentImage = currentImage

      currentImage.onload = function () {
        callback && callback()
      }
    },
    saveState: function () {
      var imageData = this.canvasCtx.getImageData(0, 0, this.state.canvasWidth, this.state.canvasHeight)

      this.state.history.push(imageData)
    },

    applyMosaicToArea: function (x, y) {
      var mosaicSize = this.state.mosaicSize

      var startX = Math.floor(x / mosaicSize) * mosaicSize
      var startY = Math.floor(y / mosaicSize) * mosaicSize

      var imageData = this.canvasCtx.getImageData(startX, startY, mosaicSize, mosaicSize)
      var data = imageData.data

      // 计算马赛克块的平均颜色
      var red = 0,
        green = 0,
        blue = 0,
        count = 0
      for (var i = 0; i < data.length; i += 4) {
        red += data[i]
        green += data[i + 1]
        blue += data[i + 2]
        count++
      }

      red = Math.round(red / count)
      green = Math.round(green / count)
      blue = Math.round(blue / count)

      // 设置马赛克块的颜色
      for (var i = 0; i < data.length; i += 4) {
        data[i] = red
        data[i + 1] = green
        data[i + 2] = blue
      }

      this.canvasCtx.putImageData(imageData, startX, startY)
    },

    exportImage() {
      function dataURLtoFile(dataURL, filename) {
        var arr = dataURL.split(',')
        var mime = arr[0].match(/:(.*?);/)[1]
        var bstr = atob(arr[1])
        let n = bstr.length
        var u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        return new File([u8arr], filename, { type: mime })
      }

      // 将 Canvas 内容转换为图片数据
      var dataURL = this.canvasEL.toDataURL('image/png', 1.0)

      var file = dataURLtoFile(dataURL, 'mosaic.png')

      return file
    },

    drawMosaic(event) {
      if (!this.state.isDrawing) {
        return
      }

      var rect = this.canvasEL.getBoundingClientRect()

      // 计算鼠标在 Canvas 中的实际位置
      // 计算宽度缩放比例
      var scaleX = this.state.canvasWidth / rect.width
      // 计算高度缩放比例
      var scaleY = this.state.canvasHeight / rect.height
      // 将鼠标 X 坐标映射到实际 Canvas 坐标
      var x = (event.clientX - rect.left) * scaleX
      // 将鼠标 Y 坐标映射到实际 Canvas 坐标
      var y = (event.clientY - rect.top) * scaleY

      this.applyMosaicToArea(x, y)
    },

    undo() {
      // 确保有可以撤销的历史状态
      if (this.state.history.length > 1) {
        // 获取上一步的状态
        var previousImageData = this.state.history[this.state.history.length - 1]
        // 移除当前状态
        this.state.history.pop()
        // 恢复 Canvas 到上一步状态
        this.canvasCtx.putImageData(previousImageData, 0, 0)
      }
    }
  }

  ImageMosaic.prototype.constructor = ImageMosaic

  return function (plugin, success) {
    return new ImageMosaic(plugin, success)
  }
})
