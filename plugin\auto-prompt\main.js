﻿/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
//输入文字，自动提示匹配字符
define(['simple!core/template', 'simple!core/store', 'simple!core/form'], function (Template, Store, Form) {

    var autoPrompt = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    autoPrompt.prototype = {
        getData: function (storeUrl) {
            var me = this;
            Store([storeUrl]).load([
                {aliases: 'list'}
            ]).done(function (store, data) {
                me.config.store = data.list.data;

                if (me.config.groupIndex && me.config.groupIndex.length > 0) {
                    var tempStore = {};
                    for (var n = 0; n < me.config.groupIndex.length; n++) {
                        var temp = [];
                        var dataKey = me.config.groupIndex[n];
                        var groupData = me.config.store[dataKey];
                        for (var i = 0; i < groupData.length; i++) {
                            temp.push({
                                key: groupData[i][me.config.index.key],
                                value: groupData[i][me.config.index.value],
                                search: groupData[i][me.config.index.search].toLowerCase()
                            });
                        }
                        tempStore[dataKey] = temp;
                    }
                    me.config.store = tempStore;
                } else {
                    var temp = [];
                    for (var i = 0; i < me.config.store.length; i++) {
                        temp.push({
                            key: me.config.store[i][me.config.index.key],
                            value: me.config.store[i][me.config.index.value],
                            search: me.config.store[i][me.config.index.search].toLowerCase()
                        });
                    }
                    me.config.store = temp;
                }
                me.renderTemplate();
            })
        },

        renderTemplate: function () {
            var me = this;
            var configData = me.config;
            var initValue = me.config.value ? me.config.value : '';
            if(me.config.defaultVal){
                initValue = me.config.store[0]['key'];
            }
            Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                var inputPrompt = getItem('input-prompt');
                var hiddenInput = getItem('hiddenInput');
                var dataList = getItem('data-list');
                var dataItems = dataList.find('li');
                var selectList = getItem('select-list');

                var selectItemKey = [];
                var showItems, hideItems;
                var index = -1;
                var isMulti = configData.isMulti;
                var customize = configData.customize ? true : false;
                showItems = dataItems;
                if (initValue) {
                    selectItemKey = (initValue + '').split(',');
                }
                var selectItemValue = [];
                for (var k = 0; k < selectItemKey.length; k++) {
                    var tempKey = dataList.find('[data-key="' + selectItemKey[k] + '"]').eq(0).attr('data-value');
                    selectItemValue.push(tempKey);
                }

                for (var n = 0; n < selectItemValue.length; n++) {
                    var tempLi = $('<li>' + selectItemValue[n] + '<span data-item="delete-item">x</span></li>');
                    selectList.append(tempLi);
                    tempLi.find('span').on('click', function (e) {
                        e.stopPropagation();
                        var delVal = $(this).parent().text();
                        var index1;
                        delVal = delVal.substr(0, delVal.length - 1);
                        for (var j = 0; j < selectItemValue.length; j++) {
                            if (selectItemValue[j] == delVal) {
                                index1 = j;
                            }
                        }
                        selectItemValue.splice(index1, 1);
                        selectItemKey.splice(index1, 1);
                        $(this).parent().remove();
                        hiddenInput.val(selectItemKey.join(','));
                        me.success.call(me, hiddenInput.val());
                    });
                }

                getItem('select-div').on('click', function (event) {
                    event.stopPropagation();
                    inputPrompt.focus();
                });
                selectList.on('click', function (e) {
                    e.stopPropagation();
                })
                inputPrompt.on('focus', function () {
                    dataList.show();
                    var inputVal = this.value.trim();
                    if (inputVal != '') {
                        showItems.show();
                        showItems.eq(0).addClass('highlight');
                    } else {
                        dataList.scrollTop(0);
                        showItems = dataItems;
                        showItems.show();
                    }
                }).on('blur', function () {
                    index = -1;
                    selectList.find('li').removeClass('bordered');
                    dataItems.removeClass('highlight')
                    dataList.hide();
                    var inputVal = this.value.trim().toLowerCase();
                    if (inputVal !== '') {
                        showItems = dataItems.filter('li[data-search*="' + inputVal + '"]')
                        if (showItems.length == 0) {
                            $(this).val('');
                        }
                    } else {
                        showItems = dataItems;
                    }
                });

                showItems.on('mousedown', function () {
                    selectList.find('li').removeClass('bordered');
                    inputPrompt.val('');
                    var flag = false;
                    var itemValue = $(this).attr('data-value');
                    var itemKey = $(this).attr('data-key');
                    for (var i = 0; i < selectItemValue.length; i++) {
                        if (selectItemValue[i] == itemValue) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        var itemSelect = $('<li>' + itemValue + '<span data-item="delete-item">x</span></li>')
                        if (!isMulti) {
                            selectList.html('');
                            selectList.append(itemSelect);
                            selectItemValue = [];
                            selectItemKey = [];
                            selectItemValue.push(itemValue);
                            selectItemKey.push(itemKey)
                            hiddenInput.val(selectItemKey[0]);
                        } else {
                            selectItemValue.push(itemValue);
                            selectItemKey.push(itemKey)
                            selectList.append(itemSelect);
                            hiddenInput.val(selectItemKey.join(','));
                        }
                        itemSelect.find('span').on('click', function (e) {
                            e.stopPropagation();
                            var delVal = $(this).parent().text();
                            var index;
                            delVal = delVal.substr(0, delVal.length - 1);
                            for (var j = 0; j < selectItemValue.length; j++) {
                                if (selectItemValue[j] == delVal) {
                                    index = j;
                                }
                            }
                            selectItemValue.splice(index, 1);
                            selectItemKey.splice(index, 1);
                            $(this).parent().remove();
                            hiddenInput.val(selectItemKey.join(','));
                            me.success.call(me, hiddenInput.val());
                        });
                        me.success.call(me, hiddenInput.val());
                    }
                });

                inputPrompt.on('input', function () {
                    selectList.find('li').removeClass('bordered');
                    var val = this.value.trim().toLowerCase();
                    dataList.show();
                    dataItems.show();
                    if (val !== '') {
                        hideItems = dataItems.filter(':not(li[data-search*="' + val + '"])');
                        showItems = dataItems.filter('li[data-search*="' + val + '"]');
                        hideItems.hide();
                        if (hideItems.length == dataItems.length) {
                            dataList.hide();
                            index = -1;
                        } else {
                            dataItems.removeClass('highlight');
                            index = 0;
                            showItems.eq(index).addClass('highlight');
                        }
                    } else {
                        index = -1;
                        dataList.scrollTop(0);
                        dataItems.removeClass('highlight');
                        showItems = dataItems;
                    }
                });
                inputPrompt.on('keydown', function (e) {
                    e.stopPropagation();
                    var len = showItems.length;
                    if (len > 0) {
                        dataList.show();
                        showItems.show();
                    }
                    if (e.keyCode == 8 && $(this).val().length == 0) {
                        var delItem = selectList.find('li:last');
                        if (delItem.hasClass('bordered')) {
                            delItem.remove();
                            selectItemValue.pop();
                            selectItemKey.pop();
                            hiddenInput.val(selectItemKey.join(','));
                        } else {
                            delItem.addClass('bordered');
                        }

                    } else if (e.keyCode == 38) {
                        e.preventDefault();
                        selectList.find('li').removeClass('bordered');
                        if (index <= 0) {
                            index = len - 1;
                        } else {
                            index--;
                        }
                        dataItems.removeClass('highlight')
                        showItems.eq(index).addClass('highlight');
                        dataList.scrollTop(index * 30);
                    } else if (e.keyCode == 40) {
                        e.preventDefault();
                        selectList.find('li').removeClass('bordered');
                        if (index >= len - 1) {
                            index = 0;
                        } else {
                            index++;
                        }
                        dataItems.removeClass('highlight')
                        showItems.eq(index).addClass('highlight');
                        dataList.scrollTop(index * 30);
                    } else if (e.keyCode == 13) {
                        e.preventDefault();
                        var flag = false;
                        var itemValue;
                        var itemKey;
                        if (index == -1) {
                            if (!customize) {
                                inputPrompt.val('');
                                return;
                            } else {
                                itemValue = inputPrompt.val().trim();
                                itemKey = itemValue;
                                if (!itemValue) {
                                    return;
                                }
                            }
                        } else {
                            itemValue = showItems.eq(index).attr('data-value');
                            itemKey = showItems.eq(index).attr('data-key');
                        }

                        for (var i = 0; i < selectItemValue.length; i++) {
                            if (selectItemValue[i] == itemValue) {
                                flag = true;
                                break;
                            }
                        }
                        if (!flag) {
                            var itemSelect = $('<li>' + itemValue + '<span data-item="delete-item">x</span></li>')
                            if (!isMulti) {
                                selectList.html('');
                                selectList.append(itemSelect);
                                selectItemValue = [];
                                selectItemKey = [];
                                selectItemValue.push(itemValue);
                                selectItemKey.push(itemKey);
                                hiddenInput.val(selectItemKey[0]);
                            } else {
                                selectItemValue.push(itemValue);
                                selectItemKey.push(itemKey);
                                selectList.append(itemSelect);
                                hiddenInput.val(selectItemKey.join(','));
                            }
                            itemSelect.find('span').on('click', function (e) {
                                e.stopPropagation();
                                var delVal = $(this).parent().text();
                                var index2;
                                delVal = delVal.substr(0, delVal.length - 1);
                                for (var j = 0; j < selectItemValue.length; j++) {
                                    if (selectItemValue[j] == delVal) {
                                        index2 = j;
                                    }
                                }
                                selectItemValue.splice(index2, 1);
                                selectItemKey.splice(index2, 1);
                                $(this).parent().remove();
                                hiddenInput.val(selectItemKey.join(','));

                            });
                        }
                        me.success.call(me, hiddenInput.val());
                        selectList.find('li').removeClass('bordered');
                        dataItems.removeClass('highlight');
                        dataList.scrollTop(0);
                        inputPrompt.val('');
                        dataList.hide();
                        showItems.hide();
                        showItems = dataItems;
                        index = -1;
                    }
                });
            }, {config: configData}).render()
        },

        // 渲染
        render: function () {
            var me = this;
            if (typeof me.config.store == 'string') {
                me.getData(me.config.store);

            } else if (typeof me.config.store == 'object') {
                for (var i = 0; i < me.config.store.length; i++) {
                    me.config.store[i].search = me.config.store[i].search.toLowerCase();
                }
                me.renderTemplate();
            }
        }
    }

    autoPrompt.prototype.constructor = autoPrompt;

    return function (plugin, success) {
        return new autoPrompt(plugin, success);
    }

});