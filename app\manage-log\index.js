/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!app/layout/main',
  'dianping!app/user-detail/index'], function (Template, Table, Utils, Widgets, Store, Form, Layout, UserDetail) {

  var actions = [
    {key: 'addJinghua', value: '添加精华'},
    {key: 'removeJinghua', value: '取消精华'},
    {key: 'delete', value: '删除'},
    {key: 'auditPass', value: '审核通过'},
    {key: 'auditIgnore', value: '审核忽略'},
    {key: 'recover', value: '恢复'},
    {key: 'forbidden', value: '禁言'},
    {key: 'removeForbidden', value: '移除禁言'},
  ]

  var actionMap = {
    'addJinghua': '添加精华',
    'removeJinghua': '取消精华',
    'delete': '删除',
    'auditPass': '审核通过',
    'auditIgnore': '审核忽略',
    'recover': '恢复',
    'forbidden': '禁言',
    'removeForbidden': '移除禁言',
  }

  var list = function (panel) {
    Table({
      description: '管理日志',
      title: '管理日志',

      buttons: {
        top: [
          {
            name: '刷新',
            class: 'info',
            click: function (obj) {
              obj.render();
            }
          }
        ],
        bottom: [
          {
            name: '刷新',
            class: 'info',
            click: function (obj) {
              obj.render();
            }
          }
        ]
      },
      search: [
        {
          xtype: 'select',
          dataIndex: 'userType',
          store: [
            //添加精华： addJinghua  取消精华：removeJinghua 删除评论： deleteDianping 审核通过： auditDianping
            {
              key: '',
              value: '请选择用户类型'
            },
            {
              key: 'sso',
              value: 'sso用户'
            },
            {
              key: 'system',
              value: '系统'
            }
          ]
        },
        {
          xtype: 'select',
          dataIndex: 'action',
          store: [
            //添加精华： addJinghua  取消精华：removeJinghua 删除评论： deleteDianping 审核通过： auditDianping
            {
              key: '',
              value: '请选择动作'
            }
          ].concat(actions)
        },
        {
          xtype: 'text',
          dataIndex: 'userId',
          placeholder: '请输入用户编号'
        },
        {
          xtype: 'text',
          dataIndex: 'entityId',
          placeholder: '请输入实体编号'
        }
      ],
      operations: [
        {
          name: '查看',
          xtype: 'view',
          width: 400,
          class: 'success',
          title: '查看',
          store: 'dianping!manage-log/data/view',
          columns: [
            {
              header: '#',
              dataIndex: 'id'
            },
            {
              header: 'SSO编号：',
              dataIndex: 'ssoUserId'
            },
            {
              header: '木仓编号：',
              dataIndex: 'mucangId'
            },
            {
              header: '类型：',
              dataIndex: 'type'
            },
            {
              header: '动作：',
              dataIndex: 'action'
            },
            {
              header: '实体编号：',
              dataIndex: 'entityId',

            },
            {
              header: '描述：',
              dataIndex: 'description'
            },
            {
              header: '扩展数据：',
              dataIndex: 'extraData'
            },
            {
              header: '创建时间：',
              dataIndex: 'createTime'
            }

          ]
        },
        {
          name: '删除',
          class: 'danger',
          xtype: 'delete',
          store: 'dianping!manage-log/data/delete'
        },
        {
          name: '查看用户',
          class: 'info',
          //lineSelector: true,
          render: function (name, allData, index) {
            if (allData[index].type == 'user')
              return '查看用户';
          },
          click: function (table, row, lineData) {

            if (arguments[2].type == 'user') {
              var panel = Layout.panel({
                id: 'user-' + lineData.id,
                name: lineData.nickname
              });
              UserDetail.edit(panel, {id: lineData.id, authorId: lineData.entityId});
            }
          }
        }
      ],
      columns: [
        {
          header: '#',
          dataIndex: 'id',
          width: 20
        },
        {
          header: '昵称',
          dataIndex: 'nickname'
        },
        {
          header: '用户类型',
          dataIndex: 'userType'
        },
        {
          header: '用户编号',
          dataIndex: 'userId'
        },
        {
          header: '类型',
          dataIndex: 'type'
        },
        {
          header: '动作',
          dataIndex: 'action',
          render: function (data) {
            var val = '';
            $.each(actions, function (i, obj) {
              if (data == obj.key) {
                val = obj.value;
              }
            });
            return val;
          }
        },
        {
          header: '实体编号',
          dataIndex: 'entityId',
          width: 380,
          render: function (data) {
            return '<span class="entityId">' + data + '</span>';
          }
        },
        {
          header: '内容',
          dataIndex: 'content',
          width: 300
        },
        {
          header: '创建时间',
          dataIndex: 'createTime',
          render: function (data) {
            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
          }
        }

      ]
    }, ['dianping!manage-log/data/list'], panel, function () {
      $('.entityId').on('click', function () {
        $(this).parent().parent().find("[title=查看用户]").click();
      });

    }).render();
  };

  return {
    list: list
  }

});